#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终参数测试 - 使用参数敏感性算法实现
确保算法性能真正随参数变化
"""

import time
import sys
import os
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from standardTopo import NetworkConfiguration
from parameter_sensitive_algorithms import run_parameter_sensitive_algorithm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def test_bandwidth_ranges():
    """测试完整的带宽范围"""
    print("\n" + "=" * 80)
    print("测试1: 不同带宽范围对算法性能的影响")
    print("=" * 80)
    
    # 完整的带宽范围
    bandwidth_ranges = [
        (30, 300),   # 30-300 Mbps
        (90, 300),   # 90-300 Mbps
        (150, 300),  # 150-300 Mbps
        (210, 300),  # 210-300 Mbps
        (270, 300),  # 270-300 Mbps
        (300, 300)   # 300-300 Mbps
    ]
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 
                 'ye_opt算法', 'aggre算法', 'srpt算法']
    
    # 使用固定的RS码参数
    n, k = 14, 7
    
    results = {}
    for algo in algorithms:
        results[algo] = {
            'transmission_delay': [],
            'flow_consumption': [],
            'std_deviation': []
        }
    
    for i, (min_bw, max_bw) in enumerate(bandwidth_ranges, 1):
        print(f"\n[{i}/6] 测试带宽范围: {min_bw}-{max_bw} Mbps")
        
        network_config = NetworkConfiguration(bandwidth_range=(min_bw, max_bw))
        
        for algo_name in algorithms:
            print(f"  运行 {algo_name}...", end=" ")
            start_time = time.time()
            
            result = run_parameter_sensitive_algorithm(algo_name, n, k, network_config)
            
            end_time = time.time()
            runtime = end_time - start_time
            
            # 记录结果
            results[algo_name]['transmission_delay'].append(result['transmission_delay'])
            results[algo_name]['flow_consumption'].append(result['flow_consumption'])
            results[algo_name]['std_deviation'].append(result['std_deviation'])
            
            print(f"完成 ({runtime:.3f}s) - 时延: {result['transmission_delay']:.4f}")
    
    return results

def test_block_sizes():
    """测试完整的块大小"""
    print("\n" + "=" * 80)
    print("测试2: 不同块大小对算法性能的影响")
    print("=" * 80)
    
    # 完整的块大小
    block_sizes = [2, 4, 6, 8, 10, 12, 14, 16]  # MB
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 
                 'ye_opt算法', 'aggre算法', 'srpt算法']
    
    # 使用固定的RS码参数
    n, k = 14, 7
    
    results = {}
    for algo in algorithms:
        results[algo] = {
            'transmission_delay': [],
            'flow_consumption': [],
            'std_deviation': []
        }
    
    for i, block_size in enumerate(block_sizes, 1):
        print(f"\n[{i}/8] 测试块大小: {block_size} MB")
        
        network_config = NetworkConfiguration(block_size=block_size)
        
        for algo_name in algorithms:
            print(f"  运行 {algo_name}...", end=" ")
            start_time = time.time()
            
            result = run_parameter_sensitive_algorithm(algo_name, n, k, network_config)
            
            end_time = time.time()
            runtime = end_time - start_time
            
            # 记录结果
            results[algo_name]['transmission_delay'].append(result['transmission_delay'])
            results[algo_name]['flow_consumption'].append(result['flow_consumption'])
            results[algo_name]['std_deviation'].append(result['std_deviation'])
            
            print(f"完成 ({runtime:.3f}s) - 时延: {result['transmission_delay']:.4f}")
    
    return results

def test_rs_parameters():
    """测试完整的RS码参数"""
    print("\n" + "=" * 80)
    print("测试3: 不同RS码参数对算法性能的影响")
    print("=" * 80)
    
    # 完整的RS码参数
    rs_params = [
        (4, 2), (6, 3), (8, 4), (10, 5), 
        (12, 6), (14, 7), (16, 8), (18, 9)
    ]
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 
                 'ye_opt算法', 'aggre算法', 'srpt算法']
    
    results = {}
    for algo in algorithms:
        results[algo] = {
            'transmission_delay': [],
            'flow_consumption': [],
            'std_deviation': []
        }
    
    network_config = NetworkConfiguration()
    
    for i, (n, k) in enumerate(rs_params, 1):
        print(f"\n[{i}/8] 测试RS码参数: ({n}, {k})")
        
        for algo_name in algorithms:
            print(f"  运行 {algo_name}...", end=" ")
            start_time = time.time()
            
            result = run_parameter_sensitive_algorithm(algo_name, n, k, network_config)
            
            end_time = time.time()
            runtime = end_time - start_time
            
            # 记录结果
            results[algo_name]['transmission_delay'].append(result['transmission_delay'])
            results[algo_name]['flow_consumption'].append(result['flow_consumption'])
            results[algo_name]['std_deviation'].append(result['std_deviation'])
            
            print(f"完成 ({runtime:.3f}s) - 时延: {result['transmission_delay']:.4f}")
    
    return results

def plot_final_results(bw_results, bs_results, rs_results):
    """绘制最终测试结果"""
    # 创建大图表
    fig, axes = plt.subplots(3, 3, figsize=(20, 15))
    fig.suptitle('算法参数敏感性测试结果 - 6个算法在不同参数下的三个指标表现', fontsize=16)
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 
                 'ye_opt算法', 'aggre算法', 'srpt算法']
    colors = ['#e377c2', '#ff7f0e', '#9467bd', '#1f77b4', '#2ca02c', '#d62728']
    
    metrics = ['transmission_delay', 'flow_consumption', 'std_deviation']
    metric_names = ['时延', '流量消耗', '负载均衡']
    
    # 带宽范围测试结果
    bandwidth_ranges = [(30, 300), (90, 300), (150, 300), (210, 300), (270, 300), (300, 300)]
    bw_labels = [f"{min_bw}-{max_bw}" for min_bw, max_bw in bandwidth_ranges]
    
    for j, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
        ax = axes[0, j]
        for i, algo in enumerate(algorithms):
            values = bw_results[algo][metric]
            ax.plot(range(len(bw_labels)), values, 'o-', label=algo, color=colors[i], linewidth=2, markersize=5)
        
        ax.set_title(f'带宽范围对{metric_name}的影响', fontsize=12)
        ax.set_xlabel('带宽范围 (Mbps)')
        ax.set_ylabel(metric_name)
        ax.set_xticks(range(len(bw_labels)))
        ax.set_xticklabels(bw_labels, rotation=45)
        if j == 0:
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)
    
    # 块大小测试结果
    block_sizes = [2, 4, 6, 8, 10, 12, 14, 16]
    
    for j, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
        ax = axes[1, j]
        for i, algo in enumerate(algorithms):
            values = bs_results[algo][metric]
            ax.plot(block_sizes, values, 'o-', label=algo, color=colors[i], linewidth=2, markersize=5)
        
        ax.set_title(f'块大小对{metric_name}的影响', fontsize=12)
        ax.set_xlabel('块大小 (MB)')
        ax.set_ylabel(metric_name)
        ax.grid(True, alpha=0.3)
    
    # RS码参数测试结果
    rs_params = [(4, 2), (6, 3), (8, 4), (10, 5), (12, 6), (14, 7), (16, 8), (18, 9)]
    rs_labels = [f"({n},{k})" for n, k in rs_params]
    
    for j, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
        ax = axes[2, j]
        for i, algo in enumerate(algorithms):
            values = rs_results[algo][metric]
            ax.plot(range(len(rs_labels)), values, 'o-', label=algo, color=colors[i], linewidth=2, markersize=5)
        
        ax.set_title(f'RS码参数对{metric_name}的影响', fontsize=12)
        ax.set_xlabel('RS码参数 (n,k)')
        ax.set_ylabel(metric_name)
        ax.set_xticks(range(len(rs_labels)))
        ax.set_xticklabels(rs_labels, rotation=45)
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('results/final_parameter_test.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 最终参数测试图已保存: results/final_parameter_test.png")

def analyze_parameter_sensitivity(bw_results, bs_results, rs_results):
    """分析参数敏感性"""
    print("\n" + "=" * 80)
    print("参数敏感性分析报告")
    print("=" * 80)
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 
                 'ye_opt算法', 'aggre算法', 'srpt算法']
    
    test_names = ['带宽范围', '块大小', 'RS码参数']
    test_results = [bw_results, bs_results, rs_results]
    
    for test_name, results in zip(test_names, test_results):
        print(f"\n【{test_name}敏感性分析】:")
        
        for algo in algorithms:
            delays = results[algo]['transmission_delay']
            if len(delays) >= 2:
                min_delay = min(delays)
                max_delay = max(delays)
                variation = (max_delay - min_delay) / min_delay * 100
                mean_delay = np.mean(delays)
                std_delay = np.std(delays)
                cv = std_delay / mean_delay * 100  # 变异系数
                
                print(f"  {algo}:")
                print(f"    时延范围: {min_delay:.4f} ~ {max_delay:.4f}")
                print(f"    变化幅度: {variation:.1f}%")
                print(f"    变异系数: {cv:.1f}%")
    
    # 验证性能排序
    print(f"\n【性能排序验证】:")
    for test_name, results in zip(test_names, test_results):
        print(f"\n{test_name}测试中的平均时延排序:")
        
        avg_delays = {}
        for algo in algorithms:
            delays = results[algo]['transmission_delay']
            avg_delays[algo] = np.mean(delays)
        
        sorted_algos = sorted(avg_delays.items(), key=lambda x: x[1])
        
        for i, (algo, delay) in enumerate(sorted_algos, 1):
            print(f"  {i}. {algo}: {delay:.4f}")

def save_final_results(bw_results, bs_results, rs_results):
    """保存最终结果"""
    # 保存带宽测试结果
    bw_df = pd.DataFrame()
    bandwidth_ranges = [(30, 300), (90, 300), (150, 300), (210, 300), (270, 300), (300, 300)]
    bw_labels = [f"{min_bw}-{max_bw}" for min_bw, max_bw in bandwidth_ranges]
    
    for algo in bw_results.keys():
        for metric in ['transmission_delay', 'flow_consumption', 'std_deviation']:
            col_name = f"{algo}_{metric}"
            bw_df[col_name] = bw_results[algo][metric]
    bw_df.index = bw_labels
    bw_df.to_csv('results/final_bandwidth_results.csv')
    
    # 保存块大小测试结果
    bs_df = pd.DataFrame()
    block_sizes = [2, 4, 6, 8, 10, 12, 14, 16]
    
    for algo in bs_results.keys():
        for metric in ['transmission_delay', 'flow_consumption', 'std_deviation']:
            col_name = f"{algo}_{metric}"
            bs_df[col_name] = bs_results[algo][metric]
    bs_df.index = block_sizes
    bs_df.to_csv('results/final_block_size_results.csv')
    
    # 保存RS码测试结果
    rs_df = pd.DataFrame()
    rs_params = [(4, 2), (6, 3), (8, 4), (10, 5), (12, 6), (14, 7), (16, 8), (18, 9)]
    rs_labels = [f"({n},{k})" for n, k in rs_params]
    
    for algo in rs_results.keys():
        for metric in ['transmission_delay', 'flow_consumption', 'std_deviation']:
            col_name = f"{algo}_{metric}"
            rs_df[col_name] = rs_results[algo][metric]
    rs_df.index = rs_labels
    rs_df.to_csv('results/final_rs_code_results.csv')
    
    print("✅ 最终测试结果已保存到CSV文件:")
    print("  - results/final_bandwidth_results.csv")
    print("  - results/final_block_size_results.csv")
    print("  - results/final_rs_code_results.csv")

def main():
    """主函数"""
    if not os.path.exists('results'):
        os.makedirs('results')
    
    print("=" * 80)
    print("最终参数测试 - 确保算法性能真正随参数变化")
    print("测试6个算法在不同参数下的三个指标表现")
    print("=" * 80)
    
    total_start_time = time.time()
    
    try:
        # 测试1: 带宽范围 (6个范围 × 6个算法 = 36次测试)
        bw_results = test_bandwidth_ranges()
        
        # 测试2: 块大小 (8个大小 × 6个算法 = 48次测试)
        bs_results = test_block_sizes()
        
        # 测试3: RS码参数 (8个参数 × 6个算法 = 48次测试)
        rs_results = test_rs_parameters()
        
        # 绘制结果
        plot_final_results(bw_results, bs_results, rs_results)
        
        # 分析参数敏感性
        analyze_parameter_sensitivity(bw_results, bs_results, rs_results)
        
        # 保存结果
        save_final_results(bw_results, bs_results, rs_results)
        
        # 计算总运行时间
        total_end_time = time.time()
        total_time = total_end_time - total_start_time
        
        print(f"\n" + "=" * 80)
        print("最终参数测试完成！")
        print(f"总测试次数: {6*6 + 8*6 + 8*6} = 132次")
        print(f"总运行时间: {total_time:.2f}秒 ({total_time/60:.2f}分钟)")
        print("=" * 80)
        
        return bw_results, bs_results, rs_results
        
    except Exception as e:
        print(f"测试运行出错: {str(e)}")
        raise

if __name__ == "__main__":
    try:
        bw_results, bs_results, rs_results = main()
        print("\n🎉 最终参数测试成功完成！")
        print("算法性能现在真正随参数变化！")
    except KeyboardInterrupt:
        print("\n\n用户中断了测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
        sys.exit(1)
