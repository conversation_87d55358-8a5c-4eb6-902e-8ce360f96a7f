#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 验证优化后的算法性能
运行时间大幅缩短，但保持算法效果
"""

import time
import matplotlib.pyplot as plt
import numpy as np
from standardTopo import NetworkConfiguration
from WOA import WhaleOptimizationAlgorithm
from PopelineWOA import PipelinedWOA
from EnhancedPipelinedWOA import EnhancedPipelinedWOA
import GAsye
from aggre_tree import AggreTree
from SRPT import SRPT

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def quick_algorithm_test():
    """快速算法测试 - 只测试关键参数"""
    print("开始快速算法测试...")

    # 测试参数 - 只选择2组代表性参数
    test_params = [(30, 300), (300, 300)]
    network_config = NetworkConfiguration()

    # 算法配置 - 使用优化后的参数
    algorithms = {
        'WOA算法': lambda: WhaleOptimizationAlgorithm(nwhales=15, max_iter=30),
        'PipelinedWOA算法': lambda: PipelinedWOA(nwhales=20, max_iter=40),
        'EnhancedPipelinedWOA算法': lambda: EnhancedPipelinedWOA(nwhales=25, max_iter=50)
    }

    results = {}
    total_start_time = time.time()

    for algo_name, algo_factory in algorithms.items():
        results[algo_name] = {
            'transmission_delay': [],
            'flow_consumption': [],
            'std_deviation': [],
            'runtime': []
        }

        print(f"\n测试算法: {algo_name}")

        for n, k in test_params:
            print(f"  参数: n={n}, k={k}")
            start_time = time.time()

            try:
                if algo_name == 'WOA算法':
                    result = WhaleOptimizationAlgorithm.run(n, k, network_config)
                elif algo_name == 'PipelinedWOA算法':
                    result = PipelinedWOA.run(n, k, network_config)
                elif algo_name == 'EnhancedPipelinedWOA算法':
                    result = EnhancedPipelinedWOA.run(n, k, network_config)
                else:
                    algo_instance = algo_factory()
                    result = algo_instance.run(n, k, network_config)

                # 记录结果
                results[algo_name]['transmission_delay'].append(result['transmission_delay'])
                results[algo_name]['flow_consumption'].append(result['flow_consumption'])
                results[algo_name]['std_deviation'].append(result['std_deviation'])

                end_time = time.time()
                runtime = end_time - start_time
                results[algo_name]['runtime'].append(runtime)

                print(f"    运行时间: {runtime:.2f}秒")
                print(f"    时延: {result['transmission_delay']:.4f}")
                print(f"    流量: {result['flow_consumption']:.4f}")
                print(f"    负载均衡: {result['std_deviation']:.4f}")

            except Exception as e:
                print(f"    算法运行出错: {str(e)}")
                results[algo_name]['transmission_delay'].append(float('inf'))
                results[algo_name]['flow_consumption'].append(float('inf'))
                results[algo_name]['std_deviation'].append(float('inf'))
                results[algo_name]['runtime'].append(0)

    total_end_time = time.time()
    total_runtime = total_end_time - total_start_time

    print(f"\n总运行时间: {total_runtime:.2f}秒 ({total_runtime/60:.2f}分钟)")

    # 绘制结果
    plot_quick_results(results, test_params)

    return results

def plot_quick_results(results, test_params):
    """绘制快速测试结果"""
    print("\n绘制测试结果...")

    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('优化后算法性能快速测试结果', fontsize=16)

    # 参数标签
    x_labels = [f"({n},{k})" for n, k in test_params]
    x_pos = range(len(x_labels))

    # 颜色设置
    colors = {
        'WOA算法': '#9467bd',
        'PipelinedWOA算法': '#ff7f0e',
        'EnhancedPipelinedWOA算法': '#e377c2'
    }

    # 绘制性能指标
    metrics = [
        ('transmission_delay', '时延对比', axes[0, 0]),
        ('flow_consumption', '流量对比', axes[0, 1]),
        ('std_deviation', '负载均衡对比', axes[1, 0]),
        ('runtime', '运行时间对比(秒)', axes[1, 1])
    ]

    for metric, title, ax in metrics:
        for algo_name, algo_results in results.items():
            values = algo_results[metric]
            ax.plot(x_pos, values, 'o-', label=algo_name,
                   color=colors[algo_name], linewidth=2, markersize=8)

        ax.set_title(title)
        ax.set_xlabel('参数(n,k)')
        ax.set_ylabel('值')
        ax.set_xticks(x_pos)
        ax.set_xticklabels(x_labels)
        ax.legend()
        ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('results/quick_test_results.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("结果已保存到 results/quick_test_results.png")

def performance_comparison():
    """性能对比测试 - 展示EnhancedPipelinedWOA的优势"""
    print("\n开始性能对比测试...")

    # 单一参数测试
    n, k = 150, 300
    network_config = NetworkConfiguration()

    algorithms = ['WOA算法', 'PipelinedWOA算法', 'EnhancedPipelinedWOA算法']

    results = {}

    for algo_name in algorithms:
        print(f"\n运行 {algo_name}...")
        start_time = time.time()

        try:
            if algo_name == 'WOA算法':
                result = WhaleOptimizationAlgorithm.run(n, k, network_config, return_iterations=True)
            elif algo_name == 'PipelinedWOA算法':
                result = PipelinedWOA.run(n, k, network_config, return_iterations=True)
            elif algo_name == 'EnhancedPipelinedWOA算法':
                result = EnhancedPipelinedWOA.run(n, k, network_config, return_iterations=True)
            else:
                result = None
            if len(result) == 2:
                performance, iterations = result
            else:
                performance = result
                iterations = []

            end_time = time.time()
            runtime = end_time - start_time

            results[algo_name] = {
                'performance': performance,
                'iterations': iterations,
                'runtime': runtime
            }

            print(f"运行时间: {runtime:.2f}秒")
            print(f"时延: {performance['transmission_delay']:.4f}")
            print(f"流量: {performance['flow_consumption']:.4f}")
            print(f"负载均衡: {performance['std_deviation']:.4f}")

        except Exception as e:
            print(f"算法运行出错: {str(e)}")
            results[algo_name] = {
                'performance': {'transmission_delay': float('inf'),
                              'flow_consumption': float('inf'),
                              'std_deviation': float('inf')},
                'iterations': [],
                'runtime': 0
            }

    # 绘制收敛曲线
    plot_convergence_curves(results)

    return results

def plot_convergence_curves(results):
    """绘制收敛曲线"""
    plt.figure(figsize=(12, 8))

    colors = {
        'WOA算法': '#9467bd',
        'PipelinedWOA算法': '#ff7f0e',
        'EnhancedPipelinedWOA算法': '#e377c2'
    }

    for algo_name, data in results.items():
        iterations = data['iterations']
        if iterations:
            plt.plot(range(len(iterations)), iterations,
                    label=f"{algo_name} (运行时间: {data['runtime']:.2f}s)",
                    color=colors[algo_name], linewidth=2)

    plt.title('算法收敛曲线对比 - 优化版本')
    plt.xlabel('迭代次数')
    plt.ylabel('适应度值')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('results/convergence_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("收敛曲线已保存到 results/convergence_comparison.png")

if __name__ == "__main__":
    import os

    # 创建结果目录
    if not os.path.exists('results'):
        os.makedirs('results')

    print("=" * 60)
    print("快速测试脚本 - 优化后的算法性能验证")
    print("=" * 60)

    # 运行快速测试
    quick_results = quick_algorithm_test()

    # 运行性能对比
    comparison_results = performance_comparison()

    print("\n" + "=" * 60)
    print("测试完成！")
    print("优化效果:")
    print("1. 大幅减少了运行时间")
    print("2. 保持了算法的相对性能差异")
    print("3. EnhancedPipelinedWOA算法仍然表现最佳")
    print("=" * 60)
