#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面测试脚本 - 测试所有6个算法
包括：基于异或合并修复树模型的4个算法 + 基于流水线化的2个算法
"""

import time
import sys
import threading
from standardTopo import NetworkConfiguration
from WOA import WhaleOptimizationAlgorithm
from PopelineWOA import PipelinedWOA
from EnhancedPipelinedWOA import EnhancedPipelinedWOA
import GAsye
from aggre_tree import AggreTree
from SRPT import SRPT

class TimeoutError(Exception):
    pass

def run_with_timeout(func, timeout_seconds=300):
    """带超时机制运行函数 - 使用线程实现"""
    result = [None]
    exception = [None]

    def target():
        try:
            result[0] = func()
        except Exception as e:
            exception[0] = e

    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout_seconds)

    if thread.is_alive():
        print(f"算法运行超时 ({timeout_seconds}秒)")
        return None
    elif exception[0] is not None:
        print(f"算法运行出错: {str(exception[0])}")
        return None
    else:
        return result[0]

def test_single_algorithm(algo_name, n, k, network_config):
    """测试单个算法"""
    print(f"\n开始测试 {algo_name}...")
    print(f"参数: n={n}, k={k}")

    start_time = time.time()

    def run_algo():
        if algo_name == 'WOA算法':
            print("  调用 WhaleOptimizationAlgorithm...")
            # 修复WOA算法调用方式
            woa = WhaleOptimizationAlgorithm(nwhales=20, max_iter=30)  # 减少参数
            return woa.run(n, k, network_config)
        elif algo_name == 'PipelinedWOA算法':
            print("  调用 PipelinedWOA.run...")
            return PipelinedWOA.run(n, k, network_config)
        elif algo_name == 'EnhancedPipelinedWOA算法':
            print("  调用 EnhancedPipelinedWOA.run...")
            return EnhancedPipelinedWOA.run(n, k, network_config)
        elif algo_name == 'ye_opt算法':
            print("  调用 GAsye.run...")
            ga = GAsye.GeneticAlgorithm_ye()
            return ga.run(n, k, network_config)
        elif algo_name == 'aggre算法':
            print("  调用 AggreTree.run...")
            return AggreTree.run(n, k, network_config)
        elif algo_name == 'srpt算法':
            print("  调用 SRPT.run...")
            return SRPT.run(n, k, network_config)
        else:
            raise ValueError(f"未知算法: {algo_name}")

    # 根据算法类型设置不同的超时时间
    timeout_map = {
        'WOA算法': 120,  # 2分钟
        'PipelinedWOA算法': 180,  # 3分钟
        'EnhancedPipelinedWOA算法': 60,  # 1分钟
        'ye_opt算法': 300,  # 5分钟
        'aggre算法': 120,  # 2分钟
        'srpt算法': 120   # 2分钟
    }

    timeout = timeout_map.get(algo_name, 300)
    result = run_with_timeout(run_algo, timeout_seconds=timeout)

    end_time = time.time()
    runtime = end_time - start_time

    if result is not None:
        print(f"  ✓ 算法运行成功")
        print(f"  运行时间: {runtime:.2f}秒")
        print(f"  时延: {result['transmission_delay']:.4f}")
        print(f"  流量: {result['flow_consumption']:.4f}")
        print(f"  负载均衡: {result['std_deviation']:.4f}")
        return {
            'success': True,
            'runtime': runtime,
            'result': result
        }
    else:
        print(f"  ✗ 算法运行失败")
        print(f"  运行时间: {runtime:.2f}秒")
        return {
            'success': False,
            'runtime': runtime,
            'result': None
        }

def comprehensive_test():
    """全面测试 - 测试所有6个算法"""
    print("=" * 80)
    print("全面测试脚本 - 测试所有6个算法")
    print("=" * 80)

    # 使用适中的参数进行测试
    n, k = 20, 50  # 进一步减小参数以确保所有算法都能运行
    network_config = NetworkConfiguration()

    print(f"网络配置信息:")
    print(f"  节点数: {len(network_config.nodes)}")
    ny_graph = network_config.get_network_graph()
    print(f"  边数: {len(ny_graph.edges())}")
    print(f"  块大小: {network_config.block_size}")

    # 按照预期性能排序的算法列表
    algorithms = [
        # 基于异或合并修复树模型的算法
        'ye_opt算法',
        'aggre算法',
        'srpt算法',
        'WOA算法',
        # 基于流水线化异或合并修复树模型的算法（应该更优）
        'PipelinedWOA算法',
        'EnhancedPipelinedWOA算法'  # 应该是最优的
    ]

    results = {}
    total_start_time = time.time()

    for algo_name in algorithms:
        try:
            result = test_single_algorithm(algo_name, n, k, network_config)
            results[algo_name] = result

            # 每个算法测试完后暂停一下
            print("  等待3秒后继续...")
            time.sleep(3)

        except KeyboardInterrupt:
            print(f"\n用户中断了 {algo_name} 的测试")
            break
        except Exception as e:
            print(f"\n测试 {algo_name} 时发生未预期错误: {str(e)}")
            results[algo_name] = {
                'success': False,
                'runtime': 0,
                'result': None
            }

    total_end_time = time.time()
    total_runtime = total_end_time - total_start_time

    print("\n" + "=" * 80)
    print("测试结果总结:")
    print("=" * 80)

    # 分类显示结果
    print("\n【基于异或合并修复树模型的算法】:")
    traditional_algos = ['ye_opt算法', 'aggre算法', 'srpt算法', 'WOA算法']
    for algo_name in traditional_algos:
        if algo_name in results:
            result = results[algo_name]
            if result['success']:
                print(f"  {algo_name}: ✓ 成功 ({result['runtime']:.2f}秒)")
                print(f"    - 时延: {result['result']['transmission_delay']:.4f}")
                print(f"    - 流量: {result['result']['flow_consumption']:.4f}")
                print(f"    - 负载均衡: {result['result']['std_deviation']:.4f}")
            else:
                print(f"  {algo_name}: ✗ 失败 ({result['runtime']:.2f}秒)")

    print("\n【基于流水线化异或合并修复树模型的算法】:")
    pipeline_algos = ['PipelinedWOA算法', 'EnhancedPipelinedWOA算法']
    for algo_name in pipeline_algos:
        if algo_name in results:
            result = results[algo_name]
            if result['success']:
                print(f"  {algo_name}: ✓ 成功 ({result['runtime']:.2f}秒)")
                print(f"    - 时延: {result['result']['transmission_delay']:.4f}")
                print(f"    - 流量: {result['result']['flow_consumption']:.4f}")
                print(f"    - 负载均衡: {result['result']['std_deviation']:.4f}")
            else:
                print(f"  {algo_name}: ✗ 失败 ({result['runtime']:.2f}秒)")

    print(f"\n总运行时间: {total_runtime:.2f}秒 ({total_runtime/60:.2f}分钟)")

    # 性能分析
    print("\n" + "=" * 80)
    print("性能分析:")
    print("=" * 80)

    successful_results = {name: result for name, result in results.items()
                         if result['success']}

    if successful_results:
        print("\n运行时间排序（从快到慢）:")
        sorted_by_time = sorted(successful_results.items(),
                               key=lambda x: x[1]['runtime'])
        for i, (name, result) in enumerate(sorted_by_time, 1):
            print(f"  {i}. {name}: {result['runtime']:.2f}秒")

        print("\n时延性能排序（从低到高）:")
        sorted_by_delay = sorted(successful_results.items(),
                                key=lambda x: x[1]['result']['transmission_delay'])
        for i, (name, result) in enumerate(sorted_by_delay, 1):
            delay = result['result']['transmission_delay']
            print(f"  {i}. {name}: {delay:.4f}")

    return results

if __name__ == "__main__":
    try:
        results = comprehensive_test()
        print("\n测试完成！")
    except KeyboardInterrupt:
        print("\n\n用户中断了测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
        sys.exit(1)
