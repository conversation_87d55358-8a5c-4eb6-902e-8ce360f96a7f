import heapq
import networkx as nx
import matplotlib.pyplot as plt
import random
import networkx.algorithms.approximation as nx_approx
import copy
import time
from standardTopo import bandWidth, blockSize, ny_graph, topo_list, num_nodes
import statistics

class SRPT:
    @staticmethod
    def run(n, k, network_config):
        """运行算法并返回结果"""
        G_srpt = copy.deepcopy(ny_graph)
        srpt_tree, srpt_solution = SRPT.build_srpt_tree(G_srpt, list(network_config.totalproviders[0]), next(iter(network_config.targets[0])))
        srpt_f1, srpt_f2, srpt_f3 = SRPT.evaluate_srpt_tree_solution([srpt_solution])
        
        return {
            'transmission_delay': srpt_f1,
            'flow_consumption': network_config.block_size * srpt_f2,
            'std_deviation': srpt_f3
        }

    @staticmethod
    def build_srpt_tree(G, helpers, root):
        """构建SRPT修复树"""
        # 如果 helpers 为 None 或者为空，直接返回空树和空路径
        if not helpers:
            return nx.Graph(), []
            
        # 如果 root 为 None，尝试选择一个默认的根节点
        if root is None and helpers:
            root = helpers[0]
            
        # 初始化空的修复树
        repair_tree = nx.Graph()
        
        # 对每个辅助节点计算到根节点的最短路径
        shortest_paths = []
        for helper in helpers:
            if helper != root:  # 不需要计算根节点到自身的路径
                shortest_path = SRPT.dijkstra(G, helper, root)
                if shortest_path:  # 只添加有效路径
                    shortest_paths.append((helper, shortest_path))
        
        # 如果没有找到任何有效路径，返回空树和空路径
        if not shortest_paths:
            return nx.Graph(), []
            
        # 对最短路径按长度升序排序
        shortest_paths.sort(key=lambda x: len(x[1]))
        visited_edges = set()
        
        # 对每个最短路径进行处理
        for helper, shortest_path in shortest_paths:
            # 将最短路径添加到修复树
            for i in range(len(shortest_path) - 1):
                u, v = shortest_path[i], shortest_path[i+1]
                if u not in repair_tree:
                    repair_tree.add_node(u)
                if v not in repair_tree:
                    repair_tree.add_node(v)
                    
                if (u, v) in visited_edges or (v, u) in visited_edges:
                    continue
                    
                visited_edges.add((u, v))
                repair_tree.add_edge(u, v, bw=G[u][v]['bw'])
                
        # 除去环
        repair_tree = SRPT.remove_cycles(repair_tree)
        
        # 找到最终节点对路径集
        final_paths = SRPT.find_solution(repair_tree, root, [h for h in helpers if h != root])
        
        return repair_tree, final_paths

    @staticmethod
    def dijkstra(G, start, end):
        """Dijkstra算法实现最短路径查找"""
        distances = {node: float('infinity') for node in G.nodes()}
        distances[start] = 0
        pq = [(0, start)]
        previous = {node: None for node in G.nodes()}

        while pq:
            current_distance, current = heapq.heappop(pq)

            if current == end:
                break

            if current_distance > distances[current]:
                continue

            for neighbor in G[current]:
                weight = 1 / G[current][neighbor]['bw'] if G[current][neighbor]['bw'] > 0 else float('infinity')
                distance = current_distance + weight

                if distance < distances[neighbor]:
                    distances[neighbor] = distance
                    previous[neighbor] = current
                    heapq.heappush(pq, (distance, neighbor))

        if previous[end] is None:
            return None

        path = []
        current = end
        while current is not None:
            path.append(current)
            current = previous[current]
        return path[::-1]

    @staticmethod
    def remove_cycles(tree):
        """移除树中的环"""
        cycles = nx.cycle_basis(tree)
        if cycles:
            cycle_to_remove = cycles[0]
            edge_to_remove = (cycle_to_remove[-1], cycle_to_remove[0])
            tree.remove_edge(*edge_to_remove)
        return tree

    @staticmethod
    def find_solution(tree, start_node, target_nodes):
        """找到从起始节点到目标节点的路径集合"""
        paths_collection = []
        for target_node in target_nodes:
            shortest_path = nx.shortest_path(tree, source=start_node, target=target_node)
            paths_collection.append(shortest_path)
        return paths_collection

    @staticmethod
    def evaluate_srpt_tree_solution(best_solution):
        """评估SRPT树的解决方案"""
        stflow = 0
        G_tree = copy.deepcopy(ny_graph)
        maxLatem = 0

        for onetree_chromosome in best_solution:
            flags = {}
            TreeweightMatrix = [[0] * num_nodes for _ in range(num_nodes)]
            for path in onetree_chromosome:
                for l in range(len(path) - 1):
                    source = path[l]
                    destination = path[l + 1]
                    if (source, destination) not in flags and (destination, source) not in flags:
                        TreeweightMatrix[source][destination] += 1
                        G_tree[source][destination]['bw'] -= blockSize
                        stflow += 1
                        flags[(source, destination)], flags[(destination, source)] = 1, 1
                    else:
                        continue
            latem = SRPT.sub_evaluate_latency(onetree_chromosome, TreeweightMatrix)
            maxLatem = max(latem, maxLatem)

        standard = SRPT.calcpsdv(G_tree, topo_list)
        return maxLatem, stflow, standard

    @staticmethod
    def sub_evaluate_latency(onetree_chromosome, TreeweightMatrix):
        """评估延迟"""
        node_count = {}
        repeated_nodes = []
        node_max_latency = {}

        for path in onetree_chromosome:
            for node in path:
                node_max_latency[node] = 0

        for path in onetree_chromosome:
            for index, node in enumerate(path[1:]):
                if node in repeated_nodes:
                    latency = 0
                    for i in range(index - 1, -1, -1):
                        source = path[i]
                        destination = path[i + 1]
                        if TreeweightMatrix[source][destination] != 0:
                            latency += blockSize / (bandWidth[source][destination] / TreeweightMatrix[source][destination])
                    node_max_latency[node] = max(node_max_latency[node], latency)

        maxLatem = 0
        for path in onetree_chromosome:
            latem = 0
            for l in range(len(path) - 1):
                source = path[l]
                destination = path[l + 1]
                if TreeweightMatrix[source][destination] != 0:
                    if source in repeated_nodes:
                        latem = node_max_latency[source] + blockSize / (bandWidth[source][destination] / TreeweightMatrix[source][destination])
                    else:
                        latem += blockSize / (bandWidth[source][destination] / TreeweightMatrix[source][destination])
                if latem > maxLatem:
                    maxLatem = latem
        return maxLatem

    @staticmethod
    def calcpsdv(G, topo_list):
        """计算标准差"""
        weight_list = [100 * (bandWidth[edge[0]][edge[1]] - G[edge[0]][edge[1]]['bw']) / bandWidth[edge[0]][edge[1]] for edge in topo_list]
        return statistics.pstdev(weight_list)

    @staticmethod
    def draw_tree(Gdraw):
        """绘制树的结构"""
        pos = nx.spring_layout(Gdraw)
        nx.draw(Gdraw, pos, with_labels=True)
        labels = {e: int(Gdraw.edges[e]['bw']) for e in Gdraw.edges}
        nx.draw_networkx_edge_labels(Gdraw, pos, edge_labels=labels)
        plt.show()
