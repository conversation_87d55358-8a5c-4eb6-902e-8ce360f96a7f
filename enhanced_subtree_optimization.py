#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的修复子树优化算法 - 考虑子树间相互影响和制衡
基于修复子树相互影响模型的优化算法实现
"""

import numpy as np
import networkx as nx
import random
import copy
from typing import List, Dict, Tu<PERSON>
from subtree_interaction_model import SubtreeInteractionModel
from standardTopo import NetworkConfiguration

class InteractionAwareOptimizer:
    """考虑相互影响的修复子树优化器"""

    def __init__(self, network_config, num_slices=4):
        self.network_config = network_config
        self.num_slices = num_slices
        self.G = network_config.get_network_graph()

        # 相互影响模型
        self.interaction_model = SubtreeInteractionModel(
            self.G, num_slices, network_config.block_size
        )

        # 优化参数
        self.alpha_competition = 0.3    # 竞争惩罚权重
        self.alpha_balancing = 0.4      # 负载均衡权重
        self.alpha_pipeline = 0.3       # 流水线协同权重

        # 资源约束
        self.max_bandwidth_utilization = 0.8  # 最大带宽利用率
        self.max_node_load = 0.7              # 最大节点负载

    def optimize_with_interactions(self, initial_subtrees: List[List[List[int]]]) -> Dict:
        """考虑相互影响的子树优化"""
        print("=" * 60)
        print("基于相互影响的修复子树优化")
        print("=" * 60)

        # 1. 分析当前子树的相互影响
        current_interactions = self.interaction_model.model_subtree_interactions(initial_subtrees)

        # 2. 识别优化机会
        optimization_opportunities = self._identify_optimization_opportunities(current_interactions)

        # 3. 应用优化策略
        optimized_subtrees = self._apply_optimization_strategies(
            initial_subtrees, optimization_opportunities
        )

        # 4. 验证优化效果
        optimized_interactions = self.interaction_model.model_subtree_interactions(optimized_subtrees)

        # 5. 计算性能改善
        performance_improvement = self._calculate_performance_improvement(
            current_interactions, optimized_interactions
        )

        return {
            'original_subtrees': initial_subtrees,
            'optimized_subtrees': optimized_subtrees,
            'original_interactions': current_interactions,
            'optimized_interactions': optimized_interactions,
            'performance_improvement': performance_improvement,
            'optimization_strategies_applied': optimization_opportunities
        }

    def _identify_optimization_opportunities(self, interactions: Dict) -> Dict:
        """识别优化机会"""
        opportunities = {
            'bandwidth_conflicts_to_resolve': [],
            'load_balancing_improvements': [],
            'pipeline_synchronization_opportunities': [],
            'resource_sharing_optimizations': []
        }

        # 1. 识别需要解决的带宽冲突
        competition_effects = interactions['competition_effects']
        for conflict_key, conflict_info in competition_effects['bandwidth_conflicts'].items():
            if conflict_info['conflict_intensity'] > 2:  # 高强度冲突
                opportunities['bandwidth_conflicts_to_resolve'].append({
                    'subtrees': conflict_key,
                    'common_edges': conflict_info['common_edges'],
                    'severity': conflict_info['conflict_intensity']
                })

        # 2. 识别负载均衡改善机会
        balancing_effects = interactions['balancing_effects']
        for edge, balance_info in balancing_effects['balancing_opportunities'].items():
            if balance_info['balancing_needed']:
                opportunities['load_balancing_improvements'].append({
                    'edge': edge,
                    'competing_subtrees': balance_info['competing_subtrees'],
                    'utilization_rate': balance_info['utilization_rate']
                })

        # 3. 识别流水线同步机会
        pipeline_effects = interactions['pipeline_effects']
        sync_potential = pipeline_effects['temporal_coordination']['synchronization_potential']
        if sync_potential > 0.2:  # 有显著同步潜力
            opportunities['pipeline_synchronization_opportunities'].append({
                'sync_potential': sync_potential,
                'time_variance': pipeline_effects['temporal_coordination']['time_variance']
            })

        # 4. 识别资源共享优化机会
        shared_resources = pipeline_effects['resource_sharing_benefits']
        for edge, subtrees in shared_resources['shared_edges'].items():
            if len(subtrees) > 2:  # 多个子树共享
                opportunities['resource_sharing_optimizations'].append({
                    'resource': edge,
                    'sharing_subtrees': subtrees,
                    'optimization_type': 'edge_sharing'
                })

        return opportunities

    def _apply_optimization_strategies(self, subtrees: List[List[List[int]]], opportunities: Dict) -> List[List[List[int]]]:
        """应用优化策略"""
        optimized_subtrees = copy.deepcopy(subtrees)

        # 策略1: 解决带宽冲突
        optimized_subtrees = self._resolve_bandwidth_conflicts(
            optimized_subtrees, opportunities['bandwidth_conflicts_to_resolve']
        )

        # 策略2: 改善负载均衡
        optimized_subtrees = self._improve_load_balancing(
            optimized_subtrees, opportunities['load_balancing_improvements']
        )

        # 策略3: 优化流水线同步
        optimized_subtrees = self._optimize_pipeline_synchronization(
            optimized_subtrees, opportunities['pipeline_synchronization_opportunities']
        )

        # 策略4: 优化资源共享
        optimized_subtrees = self._optimize_resource_sharing(
            optimized_subtrees, opportunities['resource_sharing_optimizations']
        )

        return optimized_subtrees

    def _resolve_bandwidth_conflicts(self, subtrees: List[List[List[int]]], conflicts: List[Dict]) -> List[List[List[int]]]:
        """解决带宽冲突"""
        for conflict in conflicts:
            subtree_i, subtree_j = conflict['subtrees']
            common_edges = conflict['common_edges']

            # 为冲突的子树寻找替代路径
            if subtree_i < len(subtrees) and subtree_j < len(subtrees):
                # 优先优化较小的子树
                target_subtree = subtree_i if len(subtrees[subtree_i]) <= len(subtrees[subtree_j]) else subtree_j

                new_paths = []
                for path in subtrees[target_subtree]:
                    if len(path) >= 2:
                        # 尝试找到避开冲突边的替代路径
                        alternative_path = self._find_alternative_path(
                            path[0], path[-1], common_edges
                        )
                        new_paths.append(alternative_path if alternative_path else path)
                    else:
                        new_paths.append(path)

                subtrees[target_subtree] = new_paths

        return subtrees

    def _improve_load_balancing(self, subtrees: List[List[List[int]]], improvements: List[Dict]) -> List[List[List[int]]]:
        """改善负载均衡"""
        for improvement in improvements:
            edge = improvement['edge']
            competing_subtrees = improvement['competing_subtrees']

            # 重新分配负载到不同的路径
            for subtree_id in competing_subtrees:
                if subtree_id < len(subtrees):
                    subtrees[subtree_id] = self._redistribute_subtree_load(
                        subtrees[subtree_id], edge
                    )

        return subtrees

    def _optimize_pipeline_synchronization(self, subtrees: List[List[List[int]]], opportunities: List[Dict]) -> List[List[List[int]]]:
        """优化流水线同步"""
        if not opportunities:
            return subtrees

        # 调整子树的处理顺序和路径长度以改善同步
        for i, subtree in enumerate(subtrees):
            # 根据流水线位置调整路径复杂度
            target_complexity = self._calculate_target_complexity(i, len(subtrees))
            subtrees[i] = self._adjust_subtree_complexity(subtree, target_complexity)

        return subtrees

    def _optimize_resource_sharing(self, subtrees: List[List[List[int]]], optimizations: List[Dict]) -> List[List[List[int]]]:
        """优化资源共享"""
        for optimization in optimizations:
            resource = optimization['resource']
            sharing_subtrees = optimization['sharing_subtrees']

            # 协调共享资源的使用
            subtrees = self._coordinate_resource_usage(subtrees, resource, sharing_subtrees)

        return subtrees

    def _find_alternative_path(self, source: int, target: int, avoid_edges: List[Tuple[int, int]]) -> List[int]:
        """寻找避开特定边的替代路径"""
        try:
            G_temp = copy.deepcopy(self.G)

            # 移除需要避开的边
            for edge in avoid_edges:
                if G_temp.has_edge(edge[0], edge[1]):
                    G_temp.remove_edge(edge[0], edge[1])

            # 寻找替代路径
            if nx.has_path(G_temp, source, target):
                return nx.shortest_path(G_temp, source, target)
            else:
                return None
        except:
            return None

    def _redistribute_subtree_load(self, subtree: List[List[int]], overloaded_edge: Tuple[int, int]) -> List[List[int]]:
        """重新分配子树负载"""
        new_subtree = []

        for path in subtree:
            if len(path) >= 2:
                # 检查路径是否使用了过载的边
                path_edges = [tuple(sorted([path[i], path[i+1]])) for i in range(len(path)-1)]

                if overloaded_edge in path_edges:
                    # 寻找替代路径
                    alternative = self._find_alternative_path(path[0], path[-1], [overloaded_edge])
                    new_subtree.append(alternative if alternative else path)
                else:
                    new_subtree.append(path)
            else:
                new_subtree.append(path)

        return new_subtree

    def _calculate_target_complexity(self, subtree_index: int, total_subtrees: int) -> float:
        """计算目标复杂度"""
        # 前面的子树应该更简单，后面的可以更复杂
        base_complexity = 1.0
        position_factor = (subtree_index + 1) / total_subtrees
        return base_complexity * (0.8 + 0.4 * position_factor)

    def _adjust_subtree_complexity(self, subtree: List[List[int]], target_complexity: float) -> List[List[int]]:
        """调整子树复杂度"""
        current_complexity = np.mean([len(path) for path in subtree]) if subtree else 1.0

        if current_complexity > target_complexity * 1.2:
            # 简化路径
            return self._simplify_subtree(subtree)
        elif current_complexity < target_complexity * 0.8:
            # 增加路径复杂度（如果有益）
            return self._enhance_subtree(subtree)
        else:
            return subtree

    def _simplify_subtree(self, subtree: List[List[int]]) -> List[List[int]]:
        """简化子树"""
        simplified = []
        for path in subtree:
            if len(path) > 2:
                # 尝试找到更短的路径
                try:
                    shorter_path = nx.shortest_path(self.G, path[0], path[-1])
                    simplified.append(shorter_path)
                except:
                    simplified.append(path)
            else:
                simplified.append(path)
        return simplified

    def _enhance_subtree(self, subtree: List[List[int]]) -> List[List[int]]:
        """增强子树（在有益的情况下）"""
        # 这里可以添加更多的路径或使用更高质量的路径
        return subtree  # 简化实现

    def _coordinate_resource_usage(self, subtrees: List[List[List[int]]], resource: Tuple[int, int], sharing_subtrees: List[int]) -> List[List[List[int]]]:
        """协调资源使用"""
        # 为共享资源的子树安排时间分片或路径分离
        for subtree_id in sharing_subtrees:
            if subtree_id < len(subtrees):
                # 简化实现：尝试为部分路径找到替代方案
                subtrees[subtree_id] = self._redistribute_subtree_load(subtrees[subtree_id], resource)

        return subtrees

    def _calculate_performance_improvement(self, original: Dict, optimized: Dict) -> Dict:
        """计算性能改善"""
        original_effects = original['overall_effects']
        optimized_effects = optimized['overall_effects']

        competition_improvement = (
            original_effects['total_competition_intensity'] -
            optimized_effects['total_competition_intensity']
        ) / max(original_effects['total_competition_intensity'], 1)

        efficiency_improvement = (
            optimized_effects['pipeline_efficiency_score'] -
            original_effects['pipeline_efficiency_score']
        )

        complexity_reduction = (
            original_effects['overall_interaction_complexity'] -
            optimized_effects['overall_interaction_complexity']
        ) / max(original_effects['overall_interaction_complexity'], 1)

        return {
            'competition_reduction': competition_improvement,
            'efficiency_improvement': efficiency_improvement,
            'complexity_reduction': complexity_reduction,
            'overall_improvement': (competition_improvement + efficiency_improvement + complexity_reduction) / 3
        }

def test_interaction_aware_optimization():
    """测试考虑相互影响的优化算法"""
    print("=" * 80)
    print("考虑相互影响的修复子树优化测试")
    print("=" * 80)

    # 创建网络配置
    network_config = NetworkConfiguration()

    # 获取网络中实际存在的节点
    G = network_config.get_network_graph()
    nodes = list(G.nodes())[:6]  # 使用前6个节点

    # 创建基于实际网络的测试子树
    initial_subtrees = [
        [[nodes[0], nodes[1]], [nodes[0], nodes[2]]],      # 子树1
        [[nodes[1], nodes[2]], [nodes[1], nodes[3]]],      # 子树2
        [[nodes[2], nodes[3]], [nodes[2], nodes[4]]],      # 子树3
        [[nodes[3], nodes[4]], [nodes[3], nodes[5]]]       # 子树4
    ]

    # 创建优化器
    optimizer = InteractionAwareOptimizer(network_config, num_slices=4)

    # 执行优化
    optimization_results = optimizer.optimize_with_interactions(initial_subtrees)

    # 输出结果
    print("\n【优化结果】:")
    improvement = optimization_results['performance_improvement']
    print(f"竞争减少: {improvement['competition_reduction']:.3f}")
    print(f"效率提升: {improvement['efficiency_improvement']:.3f}")
    print(f"复杂度降低: {improvement['complexity_reduction']:.3f}")
    print(f"总体改善: {improvement['overall_improvement']:.3f}")

    # 对比分析
    original_effects = optimization_results['original_interactions']['overall_effects']
    optimized_effects = optimization_results['optimized_interactions']['overall_effects']

    print(f"\n【详细对比】:")
    print(f"原始竞争强度: {original_effects['total_competition_intensity']}")
    print(f"优化后竞争强度: {optimized_effects['total_competition_intensity']}")
    print(f"原始流水线效率: {original_effects['pipeline_efficiency_score']:.3f}")
    print(f"优化后流水线效率: {optimized_effects['pipeline_efficiency_score']:.3f}")

    return optimization_results

if __name__ == "__main__":
    results = test_interaction_aware_optimization()
    print("\n🎉 考虑相互影响的修复子树优化测试完成！")
