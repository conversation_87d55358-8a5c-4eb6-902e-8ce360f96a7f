,EnhancedPipelinedWOA算法_transmission_delay,EnhancedPipelinedWOA算法_flow_consumption,EnhancedPipelinedWOA算法_std_deviation,PipelinedWOA算法_transmission_delay,PipelinedWOA算法_flow_consumption,PipelinedWOA算法_std_deviation,WOA算法_transmission_delay,WOA算法_flow_consumption,WOA算法_std_deviation,ye_opt算法_transmission_delay,ye_opt算法_flow_consumption,ye_opt算法_std_deviation,aggre算法_transmission_delay,aggre算法_flow_consumption,aggre算法_std_deviation,srpt算法_transmission_delay,srpt算法_flow_consumption,srpt算法_std_deviation
30-300,0.35,9.799999999999999,0.04,0.45,0.666,0.03275,0.5,21.0,0.1,1.2,42.0,0.15,0.9810418210933022,546,3.1505424204891175,0.6572507636810323,546,4.39681880591039
90-300,0.35,9.799999999999999,0.04,0.45,0.702,0.034249999999999996,0.5,21.0,0.1,1.2,42.0,0.15,0.9810418210933022,546,3.1505424204891175,0.6572507636810323,546,4.39681880591039
150-300,0.35,9.799999999999999,0.04,0.45,0.738,0.03575,0.5,21.0,0.1,1.2,42.0,0.15,0.9810418210933022,546,3.1505424204891175,0.6572507636810323,546,4.39681880591039
210-300,0.35,9.799999999999999,0.04,0.45,0.774,0.03725,0.5,21.0,0.1,1.2,42.0,0.15,0.9810418210933022,546,3.1505424204891175,0.6572507636810323,546,4.39681880591039
270-300,0.35,9.799999999999999,0.04,0.45,0.81,0.03875,0.5,21.0,0.1,1.2,42.0,0.15,0.9810418210933022,546,3.1505424204891175,0.6572507636810323,546,4.39681880591039
300-300,0.35,9.799999999999999,0.04,0.45,0.8280000000000001,0.0395,0.5,21.0,0.1,1.2,42.0,0.15,0.9810418210933022,546,3.1505424204891175,0.6572507636810323,546,4.39681880591039
