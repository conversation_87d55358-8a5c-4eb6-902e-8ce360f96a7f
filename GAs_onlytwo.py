# -*- coding: utf-8 -*-
import time
import random
import ctypes
import collections
import numpy as np
import copy
import math
from deap import base, creator, tools, algorithms
import heapq
from standardTopo import bandWidth, blockSize, ny_graph, topo_list, num_nodes
import networkx as nx
import networkx.algorithms.approximation as nx_approx
import matplotlib.pyplot as pyplot
import statistics


class GeneticAlgorithm_two:
    def __init__(self):
        random.seed(2)
        self.create_deap_classes()
        self.toolbox = base.Toolbox()
        self.setup_deap_toolbox()


    def create_deap_classes(self):
        creator.create("FitnessMulti", base.Fitness, weights=( -1, -1))#均衡，异或时机
        creator.create("Individual", list, fitness=creator.FitnessMulti, chromosome=None)

#  计算标准差
    def calcpsdv(self, G, topo_list):
        #weight_list = [bandWidth[edge[0]][edge[1]] - G[edge[0]][edge[1]]['bw'] for edge in topo_list]
        weight_list = [100 * (bandWidth[edge[0]][edge[1]] - G[edge[0]][edge[1]]['bw']) / bandWidth[edge[0]][edge[1]] for edge in topo_list]
        return statistics.pstdev(weight_list)

    #初始化个体和种群
    def create_individual(self):
        individual = creator.Individual()
        individual.chromosome = [[] for _ in range(len(targets))]
        for index,target in enumerate(targets):
            snodes = [random.choice(list(target))] # 根节点是当前目标和新节点随机选取
            available_providers = list(set(totalproviders[index]) - set(snodes))  # 获取对应目标的可用providers

            onetree_chromosome = [[] for _ in range(k)]
            for i in range(k):
                # 随机从n-1个中选择一个行首元素并将其从可用列表中移除，重复k次
                selected_provider = random.choice(available_providers)
                available_providers.remove(selected_provider)

                onetree_chromosome[i].append(selected_provider)
                snodes.append(selected_provider)

            T = nx_approx.steiner_tree(ny_graph, snodes, weight='true_weight', method='kou')
            for j in range(k):
                temppathk = nx.dijkstra_path(T, onetree_chromosome[j][0], snodes[0])
                if not temppathk:
                    temppathk = random.choice(list(nx.all_shortest_paths(T, onetree_chromosome[j][0], target)))
                onetree_chromosome[j] = temppathk

            individual.chromosome[index] = onetree_chromosome

        #print(f"individual.chromosome:{individual.chromosome}")
        # 计算个体的适应度
        individual.fitness.values = self.evaluate(individual)

        return individual



    def sub_evaluate_latency(self, onetree_chromosome, TreeweightMatrix):

        # 统计节点出现次数
        node_count = {}  # 记录节点出现次数
        node_maxbefore = {}
        # 遍历所有路径
        for path in onetree_chromosome:
            #for node in path[:-1]:  
            for index, node in enumerate(path[:-1]): # 不统计根节点
                # 统计节点出现次数
                if node in node_count:
                    node_count[node] += 1
                    node_maxbefore[node] = max(node_maxbefore[node], index)
                else:
                    node_count[node] = 1
                    node_maxbefore[node] = index

        # 找出出现两次以上的节点
        repeated_nodes = [node for node, count in node_count.items() if count >= 2]

        # 计算这些重复节点前面节点数量的平均值
        total_maxbefore = sum(node_maxbefore[node] for node in repeated_nodes)
        average_maxbefore = total_maxbefore / len(repeated_nodes) if repeated_nodes else 0
        num_repeated_nodes = len(repeated_nodes)

        return  average_maxbefore, num_repeated_nodes
    
    
    def evaluate(self, individual):

        G_tree = copy.deepcopy(ny_graph)

        for r in range(len(individual.chromosome)):
            flags = {}
            TreeweightMatrix = [None for i in range(num_nodes)]
            for i in range(len(TreeweightMatrix)):
                TreeweightMatrix[i] = [0 for j in range(num_nodes)]
            for i in range(len(individual.chromosome[r])):
                path = individual.chromosome[r][i]
                for l in range(len(path) - 1):
                    source = path[l]
                    destination = path[l + 1]              
                    if (r,source,destination) not in flags and (r,destination,source) not in flags:
                        # 更新TreeweightMatrix
                        TreeweightMatrix[source][destination] += 1
                        # 更新路径上的带宽
                        G_tree[source][destination]['bw'] -= blockSize
                        flags[(r,source,destination)], flags[(r,destination,source)] = 1, 1
                    else:
                        continue
            average_maxbefore, num_repeated_nodes = self.sub_evaluate_latency(individual.chromosome[r], TreeweightMatrix) 

        standard = self.calcpsdv(G_tree, topo_list)
        individual.fitness.values = ( standard, average_maxbefore)
        #individual.fitness.values = (maxLatem, stflow/10)
        return individual.fitness.values

    def has_cycle(self, chromosome):
        G = nx.DiGraph()

        # 添加路径中的边
        for path in chromosome:
            for i in range(len(path) - 1):
                G.add_edge(path[i], path[i + 1])

        # 使用深度优先搜索检测环路
        try:
            cycle_nodes = nx.find_cycle(G, orientation='original')
            return True, cycle_nodes
        except nx.NetworkXNoCycle:
            return False, []


    def rebuild_tree(self, onetree_chromosome):

        
        # 检查个体是否包含环路
        has_cycle, cycle_nodes = self.has_cycle(onetree_chromosome)

        if has_cycle:
            # 构建新的个体，保留原始个体的根节点和叶子节点
            root_node = onetree_chromosome[-1][-1]
            leaf_node = []
            tree_node = [root_node]
            for i in range(len(onetree_chromosome)):
                leaf_node.append(onetree_chromosome[i][0])
            tree_node += leaf_node
            T = nx_approx.steiner_tree(ny_graph, tree_node , weight='true_weight',method='kou')
            for j in range(k):
                paths = list(nx.all_shortest_paths(T, leaf_node[j], root_node))
                if paths:
                    temppathk = random.choice(paths)
                else:
                    temppathk = nx.dijkstra_path(T, leaf_node[j], root_node)
                onetree_chromosome[j] = temppathk

        return onetree_chromosome

    def sub_custom_mate(self, onetree_chromosome1, onetree_chromosome2):
        for cxnode in range(k):
            ind1_first_elements = [path[0] for path in onetree_chromosome1]
            ind2_first_elements = [path[0] for path in onetree_chromosome2]

            if ind1_first_elements[cxnode] not in ind2_first_elements:
                if ind2_first_elements[cxnode] not in ind1_first_elements:
                    onetree_chromosome1[cxnode], onetree_chromosome2[cxnode] = onetree_chromosome2[cxnode], onetree_chromosome2[cxnode]

        onetree_chromosome1, onetree_chromosome2 = self.rebuild_tree(onetree_chromosome1), self.rebuild_tree(onetree_chromosome2)
        
        return onetree_chromosome1, onetree_chromosome2


    def sub_custom_mutate(self, onetree_chromosome):
        node_to_change = random.randint(0, k-1)
        #print("node_to_change", node_to_change)
        allpaths = list(nx.all_shortest_paths(ny_graph, onetree_chromosome[node_to_change][0], onetree_chromosome[node_to_change][-1]))
        if allpaths:
            newindex = random.randint(0, len(allpaths) - 1)
            if len(allpaths)>1 :
                if onetree_chromosome[node_to_change] == allpaths[newindex]:
                    newindex -= 1
            onetree_chromosome[node_to_change] = allpaths[newindex]

        onetree_chromosome = self.rebuild_tree(onetree_chromosome)

        return onetree_chromosome


    def custom_mate(self, ind1, ind2):
        #交换两个森林中同一位置的某棵树
        cxnode = random.randint(0, min(len(ind1.chromosome), len(ind2.chromosome))-1)
        ind1.chromosome[cxnode], ind2.chromosome[cxnode] = ind2.chromosome[cxnode], ind1.chromosome[cxnode]
        #再对树进行交叉
        if random.random() < 0.5:
            ind1.chromosome[cxnode], ind2.chromosome[cxnode] = self.sub_custom_mate(ind1.chromosome[cxnode], ind2.chromosome[cxnode])
        return ind1, ind2


    def custom_mutate(self, ind):
        #一个森林里每棵树都进行变异
        for i in range(len(targets)):
            #print("ind.chromosome[i]", ind.chromosome[i])
            ind.chromosome[i] = self.sub_custom_mutate(ind.chromosome[i])

        return ind,

    def final_sub_evaluate(self, onetree_chromosome, TreeweightMatrix):

            # 统计节点出现次数
            node_count = {}  # 记录节点出现次数
            #print("TreeweightMatrix", TreeweightMatrix)
            # 遍历所有路径
            for path in onetree_chromosome:
                #for node in path[:-1]:  
                for index, node in enumerate(path[:-1]): # 不统计根节点
                    # 统计节点出现次数
                    if node in node_count:
                        node_count[node] += 1

                    else:
                        node_count[node] = 1


            # 找出出现两次以上的节点
            repeated_nodes = [node for node, count in node_count.items() if count >= 2]

            #print("repeated_nodes:",repeated_nodes)
            # 计算从叶子结点到重复节点的最大时延
            node_max_latency = {}  # 记录从叶子结点到重复节点的最大时延
            for path in onetree_chromosome:
                for node in path:
                    node_max_latency[node] = 0

            for path in onetree_chromosome:
                leaf_node = path[0]
                for index,node in enumerate(path[1:]):
                    if node in repeated_nodes:
                        # 计算从叶子结点到节点的时延
                        latency = 0
                        for i in range(index - 1, -1, -1):
                            source = path[i]
                            destination = path[i + 1]
                            latency += blockSize / (bandWidth[source][destination] / TreeweightMatrix[source][destination])
                       
                        node_max_latency[node] = max(node_max_latency[node], latency)
                        #print("node_max_latency[node]", node, node_max_latency[node])

            maxLatem = 0
            for i in range(len(onetree_chromosome)):
                path = onetree_chromosome[i]
                latem = 0
                for l in range(len(path) - 1):

                    source = path[l]
                    destination = path[l + 1]

                    # 更新最大传输时延
                    if TreeweightMatrix[source][destination] != 0:
                        if source in repeated_nodes:#是否重复节点，重复节点就是需要等待并进行合并操作的节点
                            latem = node_max_latency[source] + blockSize / (bandWidth[source][destination] / TreeweightMatrix[source][destination])
                        else:
                            latem += blockSize / (bandWidth[source][destination] / TreeweightMatrix[source][destination])
                    else:
                        print("TreeweightMatrix[source][destination]为0！")
                
                if latem > maxLatem:
                    maxLatem = latem


            return maxLatem

    def final_evaluate(self, individual):
        print(f"individual.chorosome:{individual.chromosome}")
        stflow = 0
        G_tree = copy.deepcopy(ny_graph)
            
        maxLatem = 0
        for r in range(len(individual.chromosome)):
            flags = {}
            TreeweightMatrix = [None for i in range(num_nodes)]
            for i in range(len(TreeweightMatrix)):
                TreeweightMatrix[i] = [0 for j in range(num_nodes)]
            for i in range(len(individual.chromosome[r])):
                path = individual.chromosome[r][i]
                for l in range(len(path) - 1):
                    source = path[l]
                    destination = path[l + 1]
                    #  print(f"source,destination:{source},{destination}")
                    if (r,source,destination) not in flags and (r,destination,source) not in flags:
                        # 更新TreeweightMatrix
                        TreeweightMatrix[source][destination] += 1
                        # 更新路径上的带宽
                        G_tree[source][destination]['bw'] -= blockSize
                        # 传输数据块
                        stflow += 1
                        flags[(r,source,destination)], flags[(r,destination,source)] = 1, 1
                    else:
                        continue
            latem = self.final_sub_evaluate(individual.chromosome[r], TreeweightMatrix) #每棵树的时延
            maxLatem = max(latem, maxLatem)

        standard = self.calcpsdv(G_tree, topo_list)
        values = [maxLatem, stflow, standard]
        #individual.fitness.values = (maxLatem, stflow/10)

        return values

    def setup_deap_toolbox(self):
        # 注册遗传操作
        self.toolbox.register("evaluate", self.evaluate)
        self.toolbox.register("mate", self.custom_mate)
        self.toolbox.register("mutate", self.custom_mutate)
        self.toolbox.register("select", tools.selNSGA2)
        # 创建个体
        self.toolbox.register("individual", self.create_individual)

    def record_pareto_front(self, population, halloffame):
        pareto_front = tools.sortNondominated(population, len(population), first_front_only=True)[0]
        # 将 Pareto 最优解添加到自定义数组中
        self.best_solutions_per_generation.append(pareto_front[0])

    def run_nsga2(self, providers, q, targets, NGEN, MU, LAMBDA, CXPB, MUTPB):

        global totalproviders, k, targets
        totalproviders, k, targets = providers, q, targets

        print("nsga2开始工作——————")
        start_time = time.time()  # 记录run_nsga2开始时间
        #random.seed(1)
        pop = []
        for _ in range(MU):
            #print("开始生成初始种群中的第",i+1,"个染色体————")
            ind = self.create_individual()
            #  print(f"Created whale: {ind.chromosome}")
            #print("成功生成初始种群中的第",i+1,"个染色体！",ind.chromosome)
            #print("初始种群中的第",i+1,"个染色体的适应度：",ind.fitness.values)
            pop.append(ind)
            #  print(f"Created whale: {pop}")

        print("种群初始化完成——————")
        hof = tools.ParetoFront()
        stats = tools.Statistics(lambda ind: ind.fitness.values)   # 用于统计每个迭代中种群的性能指标的类
        stats.register("avg", np.mean, axis=0)
        stats.register("std", np.std, axis=0)
        stats.register("min", np.min, axis=0)
        stats.register("max", np.max, axis=0)
        # halloffame：Hall of Fame，一个被保留下来的精英集合。
        # （μ+λ）进化算法
        algorithms.eaMuPlusLambda(pop, self.toolbox, MU, LAMBDA, CXPB, MUTPB, NGEN, stats, halloffame=hof,
                              verbose=False)
        end_time = time.time()  # 记录run_nsga2结束时间
        naga2time = end_time - start_time

        #for i, best_individual in enumerate(hof):
            #print(f"第 {i+1} 个Pareto最优解的目标函数值：{best_individual.fitness.values},目标函数之和：{sum(best_individual.fitness.values)}")
            #print(f"第 {i+1} 个最优解的染色体：{best_individual.chromosome}")
        #best_hof = []
        #for best in hof:
                #print("best_solution",best.chromosome)
        best_solution = hof[0]
        best_solution_value = self.final_evaluate(hof[0])
        best_solution_value.extend(hof[0].fitness.values)


        #print(f"我的最优解的放置方案：{best_solution.chromosome}")
        print(f"ga运行时间：{naga2time:.5f}秒,{naga2time/60:.5f}分钟")
        return best_solution, best_solution_value

