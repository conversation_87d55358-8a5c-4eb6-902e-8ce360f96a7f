import numpy as np
import matplotlib.pyplot as plt
import time
import networkx as nx
from standardTopo import NetworkConfiguration, bandWidth, nodes, blockSize, ny_graph, topo_list, num_nodes
from WOA import WhaleOptimizationAlgorithm
from PopelineWOA import PipelinedWOA
from EnhancedPipelinedWOA import EnhancedPipelinedWOA
import SRPT, aggre_tree, GAsye

def network_scale_test(scale_params):
    """
    不同规模网络的性能测试
    scale_params: 包含不同网络规模的参数列表，每个元素为(节点数, 边数)
    """
    results = {
        'nodes': [],
        'edges': [],
        'woa_time': [],
        'pwoa_time': [],
        'epwoa_time': [],
        'woa_quality': [],
        'pwoa_quality': [],
        'epwoa_quality': []
    }
    
    for nodes, edges in scale_params:
        # 生成对应规模的随机网络，节点从1开始编号
        node_list = list(range(1, nodes + 1))
        G = nx.gnm_random_graph(nodes, edges, seed=42)
        
        # 重新映射节点标签
        mapping = dict(zip(range(nodes), node_list))
        G = nx.relabel_nodes(G, mapping)
        
        # 创建带宽矩阵
        global bandWidth
        bandWidth = [[0 for _ in range(nodes + 1)] for _ in range(nodes + 1)]
        
        # 为边添加带宽属性并更新带宽矩阵
        global topo_list
        topo_list = []
        for (u, v) in G.edges():
            # 初始带宽设置为300
            bw_value = 300
            G[u][v]['bw'] = bw_value
            G[u][v]['weight'] = 1
            G[u][v]['true_weight'] = 1
            
            # 更新带宽矩阵（双向）
            bandWidth[u][v] = bw_value
            bandWidth[v][u] = bw_value
            
            # 更新拓扑列表
            topo_list.append((u, v))
        
        # 更新全局图
        global ny_graph
        ny_graph = G
        
        # 设置基础参数
        nwhales = 45
        max_iter = 100
        k = nodes // 2  # 设置k值为节点数的一半
        
        # 准备输入参数
        totalproviders = [[i for i in range(1, nodes)] for _ in range(1)]
        targets = [{nodes}]
        
        # 测试三种算法
        algorithms = [
            WhaleOptimizationAlgorithm(nwhales, max_iter),
            PipelinedWOA(nwhales, max_iter),
            EnhancedPipelinedWOA(nwhales, max_iter)
        ]
        
        for idx, algo in enumerate(algorithms):
            start_time = time.time()
            try:
                solution = algo.optimize(totalproviders, k, targets)
                end_time = time.time()
                
                if idx == 0:
                    results['woa_time'].append(end_time - start_time)
                    results['woa_quality'].append(solution[1][0])
                elif idx == 1:
                    results['pwoa_time'].append(end_time - start_time)
                    results['pwoa_quality'].append(solution[1][0])
                else:
                    results['epwoa_time'].append(end_time - start_time)
                    results['epwoa_quality'].append(solution[1][0])
            except Exception as e:
                print(f"测试算法 {idx} 时出错: {str(e)}")
                # 在出错时添加默认值
                if idx == 0:
                    results['woa_time'].append(float('inf'))
                    results['woa_quality'].append(float('inf'))
                elif idx == 1:
                    results['pwoa_time'].append(float('inf'))
                    results['pwoa_quality'].append(float('inf'))
                else:
                    results['epwoa_time'].append(float('inf'))
                    results['epwoa_quality'].append(float('inf'))
        
        results['nodes'].append(nodes)
        results['edges'].append(edges)
    
    return results

def parameter_sensitivity_analysis(param_ranges):
    """
    参数敏感性分析
    param_ranges: 字典，包含要测试的参数及其取值范围
    """
    results = {param: {'values': [], 'performance': []} for param in param_ranges}
    
    # 基准参数设置
    base_params = {
        'nwhales': 45,
        'max_iter': 100,
        'k': 5,
        'n': 10
    }
    
    # 对每个参数进行敏感性分析
    for param, values in param_ranges.items():
        for value in values:
            # 更新当前测试参数
            test_params = base_params.copy()
            test_params[param] = value
            
            # 准备输入参数（使用从1开始的节点编号）
            n = test_params['n']
            totalproviders = [[i for i in range(1, n)] for _ in range(1)]  # 从1开始的节点列表            targets = [{n}]  # 使用最后一个节点作为目标
            
            # 创建算法实例并测试
            algo = EnhancedPipelinedWOA(test_params['nwhales'], test_params['max_iter'])
            solution = algo.optimize(totalproviders, test_params['k'], targets)
            
            # 记录结果
            results[param]['values'].append(value)
            results[param]['performance'].append(solution[1][0])  # 使用时延作为性能指标
    
    return results

def plot_scale_test_results(results):
    """绘制不同规模网络的测试结果"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 执行时间对比
    ax1.plot(results['nodes'], results['woa_time'], 'o-', label='WOA')
    ax1.plot(results['nodes'], results['pwoa_time'], 's-', label='PipelinedWOA')
    ax1.plot(results['nodes'], results['epwoa_time'], '^-', label='EnhancedPipelinedWOA')
    ax1.set_xlabel('节点数量')
    ax1.set_ylabel('执行时间(秒)')
    ax1.set_title('算法执行时间随网络规模的变化')
    ax1.legend()
    ax1.grid(True)
    
    # 解的质量对比
    ax2.plot(results['nodes'], results['woa_quality'], 'o-', label='WOA')
    ax2.plot(results['nodes'], results['pwoa_quality'], 's-', label='PipelinedWOA')
    ax2.plot(results['nodes'], results['epwoa_quality'], '^-', label='EnhancedPipelinedWOA')
    ax2.set_xlabel('节点数量')
    ax2.set_ylabel('解的质量(时延)')
    ax2.set_title('算法性能随网络规模的变化')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig('results/scale_test_results.png')
    plt.close()

def plot_sensitivity_analysis(results):
    """绘制参数敏感性分析结果"""
    num_params = len(results)
    fig, axes = plt.subplots(1, num_params, figsize=(6*num_params, 5))
    
    for idx, (param, data) in enumerate(results.items()):
        ax = axes[idx] if num_params > 1 else axes
        ax.plot(data['values'], data['performance'], 'o-')
        ax.set_xlabel(param)
        ax.set_ylabel('性能(时延)')
        ax.set_title(f'{param}参数敏感性分析')
        ax.grid(True)
    
    plt.tight_layout()
    plt.savefig('results/sensitivity_analysis.png')
    plt.close()

def run_rs_code_comparison():
    """RS码参数对比实验"""
    rs_params = [(30, 300), (90, 300), (150, 300), (210, 300), (270, 300), (300, 300)]
    results = {}
    network_config = NetworkConfiguration()
    
    # 初始化结果字典
    algorithms = {
        'ye_opt算法': GAsye,
        'aggre算法': aggre_tree.AggreTree,
        'srpt算法': SRPT.SRPT,
        'WOA算法': WhaleOptimizationAlgorithm,
        'PipelinedWOA算法': PipelinedWOA,
        'EnhancedPipelinedWOA算法': EnhancedPipelinedWOA
    }
    
    for algo_name in algorithms.keys():
        results[algo_name] = {
            'transmission_delay': [],
            'flow_consumption': [],
            'std_deviation': []
        }
    
    # 运行实验
    for n, k in rs_params:
        print(f"\n正在运行RS码参数实验 n={n}, k={k}")
        start_time = time.time()
        
        for algo_name, algo_class in algorithms.items():
            try:
                print(f"运行算法: {algo_name}")
                if algo_name in ['WOA算法', 'PipelinedWOA算法', 'EnhancedPipelinedWOA算法']:
                    # 为WOA类算法设置特定参数
                    algo_instance = algo_class(nwhales=45, max_iter=100)
                    result = algo_instance.run(n, k, network_config)
                else:
                    # 其他算法使用标准接口
                    algo_instance = algo_class()
                    result = algo_instance.run(n, k, network_config)
                
                # 记录结果
                for metric in ['transmission_delay', 'flow_consumption', 'std_deviation']:
                    results[algo_name][metric].append(result[metric])
                    
            except Exception as e:
                print(f"算法 {algo_name} 运行出错: {str(e)}")
                # 出错时记录无穷大
                for metric in ['transmission_delay', 'flow_consumption', 'std_deviation']:
                    results[algo_name][metric].append(float('inf'))
        
        end_time = time.time()
        print(f"完成一组参数实验，耗时: {end_time - start_time:.2f}秒")
    
    # 绘制结果
    metrics = {
        'transmission_delay': '时延对比',
        'flow_consumption': '流量对比',
        'std_deviation': '负载均衡对比'
    }
    
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    x_values = [f"{n}-{k}" for n, k in rs_params]
    
    for idx, (metric, title) in enumerate(metrics.items()):
        ax = axes[idx]
        for algo_name, algo_results in results.items():
            ax.plot(range(len(x_values)), algo_results[metric], 
                   marker='o', label=algo_name, linewidth=2)
        
        ax.set_title(title)
        ax.set_xlabel('带宽范围(Mbps)')
        ax.set_ylabel('值')
        ax.set_xticks(range(len(x_values)))
        ax.set_xticklabels(x_values, rotation=45)
        ax.grid(True)
        if idx == 0:  # 只在第一个子图显示图例
            ax.legend(bbox_to_anchor=(0.5, -0.35), loc='upper center', ncol=2)
    
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.25)
    plt.savefig('results/rs_code_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return results

def run_bandwidth_comparison():
    """带宽范围对比实验"""
    bandwidth_ranges = [
        (30, 300), (90, 300), (150, 300),
        (210, 300), (270, 300), (300, 300)
    ]
    results = {}
    n, k = 14, 7  # 使用默认的RS码参数
    
    for min_bw, max_bw in bandwidth_ranges:
        print(f"Running bandwidth comparison with range {min_bw}-{max_bw}")
        network_config = NetworkConfiguration(bandwidth_range=(min_bw, max_bw))
        current_results = {}
        
        # 运行所有算法
        current_results['ye_opt算法'] = GAsye.run(n, k, network_config)
        current_results['aggre算法'] = aggre_tree.run(n, k, network_config)
        current_results['srpt算法'] = SRPT.run(n, k, network_config)
        current_results['WOA算法'] = WOA.run(n, k, network_config)
        current_results['PipelinedWOA算法'] = PopelineWOA.run(n, k, network_config)
        current_results['EnhancedPipelinedWOA算法'] = EnhancedPipelinedWOA.run(n, k, network_config)
        
        results[f"{min_bw}-{max_bw}"] = current_results
        
    plot_comparison(results, 'bandwidth', '带宽范围(Mbps)', [f"{min_bw}-{max_bw}" for min_bw, max_bw in bandwidth_ranges])
    return results

def run_block_size_comparison():
    """块大小对比实验"""
    block_sizes = [2, 4, 6, 8, 10, 12, 14, 16]
    results = {}
    n, k = 14, 7  # 使用默认的RS码参数
    
    for size in block_sizes:
        print(f"Running block size comparison with size {size}MB")
        network_config = NetworkConfiguration(block_size=size)
        current_results = {}
        
        # 运行所有算法
        current_results['ye_opt算法'] = GAsye.run(n, k, network_config)
        current_results['aggre算法'] = aggre_tree.run(n, k, network_config)
        current_results['srpt算法'] = SRPT.run(n, k, network_config)
        current_results['WOA算法'] = WOA.run(n, k, network_config)
        current_results['PipelinedWOA算法'] = PopelineWOA.run(n, k, network_config)
        current_results['EnhancedPipelinedWOA算法'] = EnhancedPipelinedWOA.run(n, k, network_config)
        
        results[size] = current_results
        
    plot_comparison(results, 'block_size', '块大小(MB)', [str(size) for size in block_sizes])
    return results

def plot_comparison(results, experiment_type, xlabel, x_values):
    """绘制对比图"""
    metrics = ['时延', '流量', '负载均衡']
    metric_keys = ['transmission_delay', 'flow_consumption', 'std_deviation']
    colors = {
        'ye_opt算法': '#1f77b4',
        'aggre算法': '#2ca02c',
        'srpt算法': '#d62728',
        'WOA算法': '#9467bd',
        'PipelinedWOA算法': '#ff7f0e',
        'EnhancedPipelinedWOA算法': '#e377c2'
    }
    
    line_widths = {
        'ye_opt算法': 1.5,
        'aggre算法': 1.5,
        'srpt算法': 1.5,
        'WOA算法': 2.5,
        'PipelinedWOA算法': 2.5,
        'EnhancedPipelinedWOA算法': 2.5
    }
    
    markers = {
        'ye_opt算法': 'o',
        'aggre算法': 's',
        'srpt算法': '^',
        'WOA算法': 'D',
        'PipelinedWOA算法': 'v',
        'EnhancedPipelinedWOA算法': '*'
    }
    
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle(f'不同{xlabel}下的算法性能对比')
    
    for i, (metric, key) in enumerate(zip(metrics, metric_keys)):
        ax = axes[i]
        
        # 获取所有值以设置合适的y轴范围
        all_values = []
        for param_results in results.values():
            for algo_results in param_results.values():
                all_values.append(algo_results[key])
        
        min_val = min(all_values)
        max_val = max(all_values)
        value_range = max_val - min_val
        
        for algo in results[list(results.keys())[0]].keys():
            values = [results[param][algo][key] for param in results.keys()]
            ax.plot(x_values, values, 
                   label=algo,
                   color=colors[algo],
                   linestyle='-',
                   marker=markers[algo],
                   linewidth=line_widths[algo],
                   markersize=8)
        
        # 设置y轴范围，留出一定空间
        ax.set_ylim(min_val - value_range * 0.1, max_val + value_range * 0.1)
        
        ax.set_title(f'{metric}对比')
        ax.set_xlabel(xlabel)
        ax.set_ylabel(metric)
        ax.legend(bbox_to_anchor=(0.5, -0.35), loc='upper center', ncol=2)
        ax.grid(True, linestyle='--', alpha=0.3)
        
        # 设置x轴标签旋转以防止重叠
        plt.setp(ax.get_xticklabels(), rotation=45)
    
    plt.tight_layout()
    plt.savefig(f'results/{experiment_type}_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()

def run_all_experiments():
    """运行所有实验"""
    print("开始运行RS码参数对比实验...")
    rs_results = run_rs_code_comparison()
    
    print("\n开始运行带宽范围对比实验...")
    bw_results = run_bandwidth_comparison()
    
    print("\n开始运行块大小对比实验...")
    bs_results = run_block_size_comparison()
    
    print("\n所有实验完成！结果已保存到 results 目录。")