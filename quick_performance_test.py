#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速性能测试 - 验证算法性能差异是否足够明显
"""

import matplotlib.pyplot as plt
import numpy as np
import os
from standardTopo import NetworkConfiguration
from WOA import WhaleOptimizationAlgorithm
from PopelineWOA import PipelinedWOA
from EnhancedPipelinedWOA import EnhancedPipelinedWOA
from SuperEnhancedPipelinedWOA import SuperEnhancedPipelinedWOA

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def run_woa_series_test():
    """测试WOA系列算法的性能差异"""
    print("=" * 80)
    print("快速性能测试 - 验证算法性能差异")
    print("=" * 80)
    
    algorithms = [
        ('WOA算法', 'WOA'),
        ('PipelinedWOA算法', 'PipelinedWOA'),
        ('EnhancedPipelinedWOA算法', 'EnhancedPipelinedWOA'),
        ('SuperEnhancedPipelinedWOA算法', 'SuperEnhancedPipelinedWOA')
    ]
    
    network_config = NetworkConfiguration()
    n, k = 10, 5  # 使用正确的RS码参数
    
    print(f"测试参数: RS码({n}, {k}), 块大小: {network_config.block_size}MB")
    
    results = {}
    
    for algo_name, algo_class in algorithms:
        print(f"\n运行 {algo_name}...")
        
        try:
            if algo_class == 'WOA':
                woa = WhaleOptimizationAlgorithm(nwhales=15, max_iter=25)
                result = woa.run(n, k, network_config)
            elif algo_class == 'PipelinedWOA':
                result = PipelinedWOA.run(n, k, network_config)
            elif algo_class == 'EnhancedPipelinedWOA':
                result = EnhancedPipelinedWOA.run(n, k, network_config)
            elif algo_class == 'SuperEnhancedPipelinedWOA':
                result = SuperEnhancedPipelinedWOA.run(n, k, network_config)
            
            results[algo_name] = result
            
            print(f"  时延: {result['transmission_delay']:.4f}")
            print(f"  流量: {result['flow_consumption']:.4f}")
            print(f"  负载均衡: {result['std_deviation']:.4f}")
            
        except Exception as e:
            print(f"  错误: {str(e)}")
            results[algo_name] = {
                'transmission_delay': 999.0,
                'flow_consumption': 999.0,
                'std_deviation': 999.0
            }
    
    return results

def analyze_performance_gaps(results):
    """分析性能差距是否足够明显"""
    print("\n" + "=" * 80)
    print("性能差距分析")
    print("=" * 80)
    
    # 按时延排序
    sorted_results = sorted(results.items(), key=lambda x: x[1]['transmission_delay'])
    
    print("1. 性能排序（按时延）:")
    for i, (algo_name, result) in enumerate(sorted_results, 1):
        status = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else "📊"
        print(f"  {i}. {algo_name}: {result['transmission_delay']:.4f} {status}")
    
    # 计算性能改进百分比
    print("\n2. 性能改进分析:")
    
    woa_delay = results.get('WOA算法', {}).get('transmission_delay', 0)
    pipelined_delay = results.get('PipelinedWOA算法', {}).get('transmission_delay', 0)
    enhanced_delay = results.get('EnhancedPipelinedWOA算法', {}).get('transmission_delay', 0)
    super_delay = results.get('SuperEnhancedPipelinedWOA算法', {}).get('transmission_delay', 0)
    
    if woa_delay > 0 and pipelined_delay > 0:
        pipelined_improvement = ((woa_delay - pipelined_delay) / woa_delay) * 100
        print(f"  PipelinedWOA vs WOA: 改进 {pipelined_improvement:.1f}%")
    
    if pipelined_delay > 0 and enhanced_delay > 0:
        enhanced_improvement = ((pipelined_delay - enhanced_delay) / pipelined_delay) * 100
        print(f"  EnhancedPipelinedWOA vs PipelinedWOA: 改进 {enhanced_improvement:.1f}%")
    
    if enhanced_delay > 0 and super_delay > 0:
        super_improvement = ((enhanced_delay - super_delay) / enhanced_delay) * 100
        print(f"  SuperEnhancedPipelinedWOA vs EnhancedPipelinedWOA: 改进 {super_improvement:.1f}%")
    
    if woa_delay > 0 and super_delay > 0:
        total_improvement = ((woa_delay - super_delay) / woa_delay) * 100
        print(f"  SuperEnhancedPipelinedWOA vs WOA: 总改进 {total_improvement:.1f}%")
    
    # 评估差距是否足够明显
    print("\n3. 差距评估:")
    
    if enhanced_delay > 0 and pipelined_delay > 0:
        enhanced_gap = abs(enhanced_delay - pipelined_delay) / pipelined_delay * 100
        if enhanced_gap >= 20:
            print(f"  ✅ Enhanced vs Pipelined差距: {enhanced_gap:.1f}% (足够明显)")
        elif enhanced_gap >= 10:
            print(f"  ⚠️ Enhanced vs Pipelined差距: {enhanced_gap:.1f}% (中等明显)")
        else:
            print(f"  ❌ Enhanced vs Pipelined差距: {enhanced_gap:.1f}% (不够明显)")
    
    if super_delay > 0 and enhanced_delay > 0:
        super_gap = abs(super_delay - enhanced_delay) / enhanced_delay * 100
        if super_gap >= 10:
            print(f"  ✅ SuperEnhanced vs Enhanced差距: {super_gap:.1f}% (足够明显)")
        elif super_gap >= 5:
            print(f"  ⚠️ SuperEnhanced vs Enhanced差距: {super_gap:.1f}% (中等明显)")
        else:
            print(f"  ❌ SuperEnhanced vs Enhanced差距: {super_gap:.1f}% (不够明显)")
    
    # 检查排序是否正确
    print("\n4. 排序验证:")
    expected_order = ['SuperEnhancedPipelinedWOA算法', 'EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法']
    actual_order = [item[0] for item in sorted_results]
    
    if actual_order[:4] == expected_order:
        print("  ✅ 性能排序完全正确！")
        print("  SuperEnhanced > Enhanced > Pipelined > WOA")
    else:
        print("  ❌ 性能排序不符合预期")
        print(f"  预期: {' > '.join(expected_order)}")
        print(f"  实际: {' > '.join(actual_order[:4])}")

def plot_performance_comparison(results):
    """绘制性能对比图"""
    if not os.path.exists('results'):
        os.makedirs('results')
    
    # 提取WOA系列算法
    woa_algorithms = ['WOA算法', 'PipelinedWOA算法', 'EnhancedPipelinedWOA算法', 'SuperEnhancedPipelinedWOA算法']
    woa_results = {algo: results[algo] for algo in woa_algorithms if algo in results}
    
    # 按时延排序
    sorted_woa = sorted(woa_results.items(), key=lambda x: x[1]['transmission_delay'])
    
    algorithms = [item[0] for item in sorted_woa]
    delays = [item[1]['transmission_delay'] for item in sorted_woa]
    
    # 创建图表
    plt.figure(figsize=(12, 8))
    
    # 设置颜色
    colors = ['#ff1493', '#e377c2', '#ff7f0e', '#9467bd']
    
    bars = plt.bar(range(len(algorithms)), delays, color=colors, alpha=0.8)
    
    # 设置标签
    plt.title('WOA系列算法性能对比', fontsize=16)
    plt.ylabel('传输时延', fontsize=14)
    plt.xticks(range(len(algorithms)), algorithms, rotation=45, ha='right')
    
    # 添加数值标签
    for i, (bar, delay) in enumerate(zip(bars, delays)):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height,
               f'{delay:.4f}', ha='center', va='bottom', fontsize=12, fontweight='bold')
    
    # 特别标注前三名
    for i in range(min(3, len(bars))):
        bars[i].set_edgecolor('gold')
        bars[i].set_linewidth(3)
        # 添加排名标注
        plt.text(i, delays[i] + max(delays) * 0.05, f'第{i+1}名',
               ha='center', va='bottom', fontsize=12, fontweight='bold', color='red')
    
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig('results/quick_performance_test.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"\n📊 性能对比图已保存到: results/quick_performance_test.png")

def main():
    """主函数"""
    try:
        # 运行测试
        results = run_woa_series_test()
        
        # 分析结果
        analyze_performance_gaps(results)
        
        # 绘制图表
        plot_performance_comparison(results)
        
        print(f"\n🎉 快速性能测试完成！")
        
    except Exception as e:
        print(f"测试出错: {str(e)}")
        raise

if __name__ == "__main__":
    main()
