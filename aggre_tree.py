# aggre_tree.py

import networkx as nx
import networkx.algorithms.approximation as nx_approx
import random
import copy
import time
import matplotlib.pyplot as plt
from standardTopo import bandWidth, blockSize, ny_graph, topo_list, num_nodes
import statistics


class AggreTree:
    def __init__(self, G, M, target):
        self.G = G
        self.M = M
        self.target = target

    @staticmethod
    def run(n, k, network_config):
        """运行算法并返回结果"""
        G_aggre = copy.deepcopy(ny_graph)
        instance = AggreTree(G_aggre, list(network_config.totalproviders[0]), next(iter(network_config.targets[0])))
        TM, solution = instance.aggre_tree_construction()
        evaluator = TreeEvaluator()
        aggre_f1, aggre_f2, aggre_f3 = evaluator.evaluate_tree_solution([solution])
        
        return {
            'transmission_delay': aggre_f1,
            'flow_consumption': network_config.block_size * aggre_f2,
            'std_deviation': aggre_f3
        }

    def calculate_min_bandwidth(self, tree):
        min_bw = min(data['bw'] for _, _, data in tree.edges(data=True))
        print("最小带宽:", min_bw)
        return min_bw

    def draw_tree(self, G):
        pos = nx.spring_layout(G)
        nx.draw(G, pos, with_labels=True)
        labels = {e: int(G.edges[e]['bw']) for e in G.edges}
        nx.draw_networkx_edge_labels(G, pos, edge_labels=labels)
        plt.show()

    def remove_cycles(self, tree):
        cycles = nx.cycle_basis(tree)

        if cycles:
            cycle_to_remove = cycles[0]
            edge_to_remove = (cycle_to_remove[-1], cycle_to_remove[0])
            tree.remove_edge(*edge_to_remove)

        return tree

    def find_solution(self, tree, start_node, target_nodes):
        paths_collection = []

        for target_node in target_nodes:
            shortest_path = nx.shortest_path(tree, source=start_node, target=target_node)
            paths_collection.append(shortest_path)

        return paths_collection

    def dijkstra_max_bottleneck_path(self, graph, source, target):
        path = nx.dijkstra_path(graph, source=source, target=target, weight=lambda u, v, d: 1 / d['bw'])
        return path

    def bandwidth_first_search(self):
        #print("开始构建最大瓶颈带宽树")
        tree = nx.Graph()
        visited_edges = set()

        for target_node in self.M:
            #print(f"进行到{target_node}...")
            try:
                path = self.dijkstra_max_bottleneck_path(self.G, source=target_node, target=self.M[0])
                #print(f"path:{path}")
                for i in range(len(path) - 1):
                    u, v = path[i], path[i + 1]

                    if u not in tree:
                        tree.add_node(u)
                    if v not in tree:
                        tree.add_node(v)

                    if (u, v) in visited_edges or (v, u) in visited_edges:
                        continue

                    visited_edges.add((u, v))
                    tree.add_edge(u, v, bw=self.G[u][v]['bw'])

            except nx.NetworkXNoPath:
                print(f"No path from {target_node} to {self.M[0]}.")

        min_bw = self.calculate_min_bandwidth(tree)
        #print("Tree Edges:", tree.edges())
        #self.draw_tree(tree)

        return tree, min_bw

    def update_topology(self, B, G):
        Gc = G.copy()

        for u, v, weight in list(Gc.edges(data=True)):
            if weight['bw'] < B:
                Gc.remove_edge(u, v)

        #self.draw_tree(Gc)
        return Gc

    def aggre_tree_construction(self):
        bwtree_s_time = time.time()
        tree, max_block_bw = self.bandwidth_first_search()
        Gc = self.update_topology(max_block_bw, self.G)

        if not nx.is_connected(Gc):
            print("Warning: Graph Gc is not connected.")
            # 找到包含所有必需节点的最小带宽阈值
            required_nodes = set(self.M + [self.target])
            while not self.check_connectivity(Gc, required_nodes):
                max_block_bw = max_block_bw * 0.9  # 降低带宽要求
                Gc = self.update_topology(max_block_bw, self.G)
            print(f"Adjusted bandwidth threshold to {max_block_bw}")

        try:
            TM = nx_approx.steiner_tree(Gc, self.M + [self.target], weight='true_weight', method='kou')
            TM_without_cycles = self.remove_cycles(TM)
            min_bw = self.calculate_min_bandwidth(TM_without_cycles)
            solution = self.find_solution(TM_without_cycles, self.target, self.M)
            bwtree_e_time = time.time()
            print(f"构建aggre树用时{bwtree_e_time - bwtree_s_time} s ")
            return TM_without_cycles, solution
        except Exception as e:
            print(f"Error in tree construction: {e}")
            # 使用备用方案：直接在原图上构建
            TM = nx_approx.steiner_tree(self.G, self.M + [self.target], weight='true_weight', method='kou')
            TM_without_cycles = self.remove_cycles(TM)
            solution = self.find_solution(TM_without_cycles, self.target, self.M)
            bwtree_e_time = time.time()
            print(f"构建aggre树用时{bwtree_e_time - bwtree_s_time} s ")
            return TM_without_cycles, solution

    def check_connectivity(self, graph, required_nodes):
        """检查所有必需节点是否在同一个连通分量中"""
        if not all(node in graph for node in required_nodes):
            return False
            
        # 获取任意一个必需节点所在的连通分量
        first_node = list(required_nodes)[0]
        component = nx.node_connected_component(graph, first_node)
        
        # 检查所有其他必需节点是否在同一个连通分量中
        return all(node in component for node in required_nodes)

    
class TreeEvaluator:
    @staticmethod
    def calcpsdv(G, topo_list):
        #weight_list = [bandWidth[edge[0]][edge[1]] - G[edge[0]][edge[1]]['bw'] for edge in topo_list]
        weight_list = [100 * (bandWidth[edge[0]][edge[1]] - G[edge[0]][edge[1]]['bw']) / bandWidth[edge[0]][edge[1]] for edge in topo_list]
        return statistics.pstdev(weight_list)

    @staticmethod
    def sub_evaluate_latency(onetree_chromosome, TreeweightMatrix):
        node_count = {}
        repeated_nodes = []
        node_max_latency = {}

        for path in onetree_chromosome:
            for node in path:
                node_max_latency[node] = 0

        for path in onetree_chromosome:
            for index, node in enumerate(path[1:]):
                if node in repeated_nodes:
                    latency = 0
                    for i in range(index - 1, -1, -1):
                        source = path[i]
                        destination = path[i + 1]

                        if TreeweightMatrix[source][destination] != 0:
                            latency += blockSize / (bandWidth[source][destination] / TreeweightMatrix[source][destination])

                    node_max_latency[node] = max(node_max_latency[node], latency)

        maxLatem = 0
        for i in range(len(onetree_chromosome)):
            path = onetree_chromosome[i]
            latem = 0
            for l in range(len(path) - 1):
                source = path[l]
                destination = path[l + 1]

                if TreeweightMatrix[source][destination] != 0:
                    if source in repeated_nodes:
                        latem = node_max_latency[source] + blockSize / (bandWidth[source][destination] / TreeweightMatrix[source][destination])
                    else:
                        latem += blockSize / (bandWidth[source][destination] / TreeweightMatrix[source][destination])

            if latem > maxLatem:
                maxLatem = latem

        return maxLatem

    @staticmethod
    def evaluate_tree_solution(best_solution):
        stflow = 0
        G_tree = copy.deepcopy(ny_graph)

        maxLatem = 0
        for onetree_chromosome in best_solution:
            flags = {}
            TreeweightMatrix = [[0] * num_nodes for _ in range(num_nodes)]
            for path in onetree_chromosome:
                for l in range(len(path) - 1):
                    source = path[l]
                    destination = path[l + 1]
                    if (source, destination) not in flags and (destination, source) not in flags:
                        TreeweightMatrix[source][destination] += 1
                        G_tree[source][destination]['bw'] -= blockSize
                        stflow += 1
                        flags[(source, destination)], flags[(destination, source)] = 1, 1
                    else:
                        continue
            latem = TreeEvaluator.sub_evaluate_latency(onetree_chromosome, TreeweightMatrix)
            maxLatem = max(latem, maxLatem)

        standard = TreeEvaluator.calcpsdv(G_tree, topo_list)

        return maxLatem, stflow, standard
