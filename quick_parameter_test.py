#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速参数测试 - 第三个问题的快速验证版本
测试关键参数组合，验证功能正确性
"""

import time
import sys
import os
import threading
import matplotlib.pyplot as plt
import numpy as np
from standardTopo import NetworkConfiguration
from WOA import WhaleOptimizationAlgorithm
from PopelineWOA import PipelinedWOA
from EnhancedPipelinedWOA import EnhancedPipelinedWOA
import GAsye
from aggre_tree import AggreTree
from SRPT import SRPT

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def run_with_timeout(func, timeout_seconds=60):
    """带超时机制运行函数"""
    result = [None]
    exception = [None]
    
    def target():
        try:
            result[0] = func()
        except Exception as e:
            exception[0] = e
    
    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout_seconds)
    
    if thread.is_alive():
        print(f"超时 ({timeout_seconds}秒)")
        return None
    elif exception[0] is not None:
        print(f"出错: {str(exception[0])}")
        return None
    else:
        return result[0]

def run_single_algorithm(algo_name, n, k, network_config):
    """运行单个算法"""
    def run_algo():
        if algo_name == 'WOA算法':
            woa = WhaleOptimizationAlgorithm(nwhales=8, max_iter=10)  # 极小参数
            return woa.run(n, k, network_config)
        elif algo_name == 'PipelinedWOA算法':
            return PipelinedWOA.run(n, k, network_config)
        elif algo_name == 'EnhancedPipelinedWOA算法':
            return EnhancedPipelinedWOA.run(n, k, network_config)
        elif algo_name == 'ye_opt算法':
            ga = GAsye.GeneticAlgorithm_ye()
            return ga.run(n, k, network_config)
        elif algo_name == 'aggre算法':
            return AggreTree.run(n, k, network_config)
        elif algo_name == 'srpt算法':
            return SRPT.run(n, k, network_config)
        else:
            raise ValueError(f"未知算法: {algo_name}")
    
    timeout_map = {
        'WOA算法': 30,
        'PipelinedWOA算法': 25,
        'EnhancedPipelinedWOA算法': 20,
        'ye_opt算法': 30,
        'aggre算法': 15,
        'srpt算法': 15
    }
    
    timeout = timeout_map.get(algo_name, 30)
    result = run_with_timeout(run_algo, timeout_seconds=timeout)
    
    if result is not None:
        return result
    else:
        return {
            'transmission_delay': float('inf'),
            'flow_consumption': float('inf'),
            'std_deviation': float('inf')
        }

def quick_test_bandwidth_ranges():
    """快速测试带宽范围（选择3个代表性范围）"""
    print("\n" + "=" * 60)
    print("快速测试1: 带宽范围对算法性能的影响")
    print("=" * 60)
    
    # 选择3个代表性带宽范围
    bandwidth_ranges = [
        (30, 300),   # 最大范围
        (150, 300),  # 中等范围
        (300, 300)   # 固定带宽
    ]
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 'srpt算法']
    n, k = 10, 5  # 使用较小参数加速测试
    
    results = {}
    for algo in algorithms:
        results[algo] = {'transmission_delay': []}
    
    for min_bw, max_bw in bandwidth_ranges:
        print(f"\n测试带宽范围: {min_bw}-{max_bw} Mbps")
        
        network_config = NetworkConfiguration(bandwidth_range=(min_bw, max_bw))
        
        for algo_name in algorithms:
            print(f"  运行 {algo_name}...", end=" ")
            start_time = time.time()
            
            result = run_single_algorithm(algo_name, n, k, network_config)
            
            end_time = time.time()
            runtime = end_time - start_time
            
            results[algo_name]['transmission_delay'].append(result['transmission_delay'])
            
            if result['transmission_delay'] != float('inf'):
                print(f"完成 ({runtime:.1f}s) - 时延: {result['transmission_delay']:.4f}")
            else:
                print(f"失败 ({runtime:.1f}s)")
    
    return results

def quick_test_block_sizes():
    """快速测试块大小（选择4个代表性大小）"""
    print("\n" + "=" * 60)
    print("快速测试2: 块大小对算法性能的影响")
    print("=" * 60)
    
    # 选择4个代表性块大小
    block_sizes = [2, 8, 14, 16]  # MB
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 'srpt算法']
    n, k = 10, 5
    
    results = {}
    for algo in algorithms:
        results[algo] = {'transmission_delay': []}
    
    for block_size in block_sizes:
        print(f"\n测试块大小: {block_size} MB")
        
        network_config = NetworkConfiguration(block_size=block_size)
        
        for algo_name in algorithms:
            print(f"  运行 {algo_name}...", end=" ")
            start_time = time.time()
            
            result = run_single_algorithm(algo_name, n, k, network_config)
            
            end_time = time.time()
            runtime = end_time - start_time
            
            results[algo_name]['transmission_delay'].append(result['transmission_delay'])
            
            if result['transmission_delay'] != float('inf'):
                print(f"完成 ({runtime:.1f}s) - 时延: {result['transmission_delay']:.4f}")
            else:
                print(f"失败 ({runtime:.1f}s)")
    
    return results

def quick_test_rs_parameters():
    """快速测试RS码参数（选择4个代表性参数）"""
    print("\n" + "=" * 60)
    print("快速测试3: RS码参数对算法性能的影响")
    print("=" * 60)
    
    # 选择4个代表性RS码参数
    rs_params = [(4, 2), (8, 4), (14, 7), (18, 9)]
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 'srpt算法']
    
    results = {}
    for algo in algorithms:
        results[algo] = {'transmission_delay': []}
    
    network_config = NetworkConfiguration()
    
    for n, k in rs_params:
        print(f"\n测试RS码参数: ({n}, {k})")
        
        for algo_name in algorithms:
            print(f"  运行 {algo_name}...", end=" ")
            start_time = time.time()
            
            result = run_single_algorithm(algo_name, n, k, network_config)
            
            end_time = time.time()
            runtime = end_time - start_time
            
            results[algo_name]['transmission_delay'].append(result['transmission_delay'])
            
            if result['transmission_delay'] != float('inf'):
                print(f"完成 ({runtime:.1f}s) - 时延: {result['transmission_delay']:.4f}")
            else:
                print(f"失败 ({runtime:.1f}s)")
    
    return results

def plot_quick_results(bw_results, bs_results, rs_results):
    """绘制快速测试结果"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('快速参数测试结果 - 算法性能对比', fontsize=16)
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 'srpt算法']
    colors = ['#e377c2', '#ff7f0e', '#9467bd', '#d62728']
    
    # 1. 带宽范围测试结果
    ax1 = axes[0]
    bandwidth_ranges = [(30, 300), (150, 300), (300, 300)]
    x_labels = [f"{min_bw}-{max_bw}" for min_bw, max_bw in bandwidth_ranges]
    
    for i, algo in enumerate(algorithms):
        delays = bw_results[algo]['transmission_delay']
        filtered_delays = [d if d != float('inf') else np.nan for d in delays]
        ax1.plot(range(len(x_labels)), filtered_delays, 'o-', label=algo, color=colors[i], linewidth=2)
    
    ax1.set_title('带宽范围对时延的影响', fontsize=14)
    ax1.set_xlabel('带宽范围 (Mbps)')
    ax1.set_ylabel('时延')
    ax1.set_xticks(range(len(x_labels)))
    ax1.set_xticklabels(x_labels, rotation=45)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 块大小测试结果
    ax2 = axes[1]
    block_sizes = [2, 8, 14, 16]
    
    for i, algo in enumerate(algorithms):
        delays = bs_results[algo]['transmission_delay']
        filtered_delays = [d if d != float('inf') else np.nan for d in delays]
        ax2.plot(block_sizes, filtered_delays, 'o-', label=algo, color=colors[i], linewidth=2)
    
    ax2.set_title('块大小对时延的影响', fontsize=14)
    ax2.set_xlabel('块大小 (MB)')
    ax2.set_ylabel('时延')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. RS码参数测试结果
    ax3 = axes[2]
    rs_params = [(4, 2), (8, 4), (14, 7), (18, 9)]
    x_labels = [f"({n},{k})" for n, k in rs_params]
    
    for i, algo in enumerate(algorithms):
        delays = rs_results[algo]['transmission_delay']
        filtered_delays = [d if d != float('inf') else np.nan for d in delays]
        ax3.plot(range(len(x_labels)), filtered_delays, 'o-', label=algo, color=colors[i], linewidth=2)
    
    ax3.set_title('RS码参数对时延的影响', fontsize=14)
    ax3.set_xlabel('RS码参数 (n,k)')
    ax3.set_ylabel('时延')
    ax3.set_xticks(range(len(x_labels)))
    ax3.set_xticklabels(x_labels, rotation=45)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('results/quick_parameter_test.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 快速测试结果图已保存: results/quick_parameter_test.png")

def main():
    """主函数 - 运行快速参数测试"""
    if not os.path.exists('results'):
        os.makedirs('results')
    
    print("=" * 80)
    print("快速参数测试 - 第三个问题的功能验证")
    print("测试关键参数组合，验证算法在不同条件下的表现")
    print("=" * 80)
    
    total_start_time = time.time()
    
    try:
        # 快速测试1: 带宽范围
        bw_results = quick_test_bandwidth_ranges()
        
        # 快速测试2: 块大小
        bs_results = quick_test_block_sizes()
        
        # 快速测试3: RS码参数
        rs_results = quick_test_rs_parameters()
        
        # 绘制结果
        plot_quick_results(bw_results, bs_results, rs_results)
        
        # 分析结果
        print("\n" + "=" * 60)
        print("快速测试结果分析:")
        print("=" * 60)
        
        algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 'srpt算法']
        
        for algo in algorithms:
            print(f"\n【{algo}】:")
            
            # 带宽测试
            bw_delays = [d for d in bw_results[algo]['transmission_delay'] if d != float('inf')]
            if bw_delays:
                print(f"  带宽测试 - 时延范围: {min(bw_delays):.4f} ~ {max(bw_delays):.4f}")
            
            # 块大小测试
            bs_delays = [d for d in bs_results[algo]['transmission_delay'] if d != float('inf')]
            if bs_delays:
                print(f"  块大小测试 - 时延范围: {min(bs_delays):.4f} ~ {max(bs_delays):.4f}")
            
            # RS码测试
            rs_delays = [d for d in rs_results[algo]['transmission_delay'] if d != float('inf')]
            if rs_delays:
                print(f"  RS码测试 - 时延范围: {min(rs_delays):.4f} ~ {max(rs_delays):.4f}")
        
        # 计算总运行时间
        total_end_time = time.time()
        total_time = total_end_time - total_start_time
        
        print(f"\n" + "=" * 80)
        print("快速参数测试完成！")
        print(f"总运行时间: {total_time:.2f}秒 ({total_time/60:.2f}分钟)")
        print("\n生成的图表文件:")
        print("  - results/quick_parameter_test.png")
        print("=" * 80)
        
        return bw_results, bs_results, rs_results
        
    except Exception as e:
        print(f"测试运行出错: {str(e)}")
        raise

if __name__ == "__main__":
    try:
        bw_results, bs_results, rs_results = main()
        print("\n🎉 快速参数测试成功完成！")
        print("功能验证通过，可以运行完整版本的 comprehensive_parameter_test.py")
    except KeyboardInterrupt:
        print("\n\n用户中断了测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
        sys.exit(1)
