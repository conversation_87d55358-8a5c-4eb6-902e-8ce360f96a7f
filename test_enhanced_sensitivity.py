#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的参数敏感性 - 验证修复后的效果
"""

import matplotlib.pyplot as plt
import numpy as np
from standardTopo import NetworkConfiguration
from parameter_sensitive_algorithms import run_parameter_sensitive_algorithm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def test_all_parameter_sensitivity():
    """测试所有参数的敏感性"""
    print("=" * 80)
    print("测试增强的参数敏感性 - 验证修复后的效果")
    print("=" * 80)
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 'srpt算法']
    
    # 1. 测试带宽敏感性
    print("\n【1. 带宽敏感性测试】:")
    bandwidth_ranges = [(30, 300), (90, 300), (150, 300), (210, 300), (270, 300), (300, 300)]
    bw_results = {}
    for algo in algorithms:
        bw_results[algo] = []
    
    for min_bw, max_bw in bandwidth_ranges:
        print(f"\n带宽范围: {min_bw}-{max_bw} Mbps")
        network_config = NetworkConfiguration(bandwidth_range=(min_bw, max_bw))
        
        for algo_name in algorithms:
            result = run_parameter_sensitive_algorithm(algo_name, 14, 7, network_config)
            delay = result['transmission_delay']
            bw_results[algo_name].append(delay)
            print(f"  {algo_name}: {delay:.4f}")
    
    # 2. 测试块大小敏感性
    print("\n【2. 块大小敏感性测试】:")
    block_sizes = [2, 4, 6, 8, 10, 12, 14, 16]
    bs_results = {}
    for algo in algorithms:
        bs_results[algo] = []
    
    for block_size in block_sizes:
        print(f"\n块大小: {block_size} MB")
        network_config = NetworkConfiguration(block_size=block_size)
        
        for algo_name in algorithms:
            result = run_parameter_sensitive_algorithm(algo_name, 14, 7, network_config)
            delay = result['transmission_delay']
            bs_results[algo_name].append(delay)
            print(f"  {algo_name}: {delay:.4f}")
    
    # 3. 测试RS码参数敏感性
    print("\n【3. RS码参数敏感性测试】:")
    rs_params = [(4, 2), (6, 3), (8, 4), (10, 5), (12, 6), (14, 7), (16, 8), (18, 9)]
    rs_results = {}
    for algo in algorithms:
        rs_results[algo] = []
    
    network_config = NetworkConfiguration()
    
    for n, k in rs_params:
        print(f"\nRS码参数: ({n}, {k}), rs_factor = {n/18.0:.3f}")
        
        for algo_name in algorithms:
            result = run_parameter_sensitive_algorithm(algo_name, n, k, network_config)
            delay = result['transmission_delay']
            rs_results[algo_name].append(delay)
            print(f"  {algo_name}: {delay:.4f}")
    
    # 分析敏感性
    print("\n" + "=" * 80)
    print("参数敏感性分析:")
    print("=" * 80)
    
    test_names = ['带宽范围', '块大小', 'RS码参数']
    test_results = [bw_results, bs_results, rs_results]
    
    for test_name, results in zip(test_names, test_results):
        print(f"\n【{test_name}敏感性分析】:")
        
        for algo in algorithms:
            delays = results[algo]
            if len(delays) >= 2:
                min_delay = min(delays)
                max_delay = max(delays)
                variation = (max_delay - min_delay) / min_delay * 100
                mean_delay = np.mean(delays)
                std_delay = np.std(delays)
                cv = std_delay / mean_delay * 100  # 变异系数
                
                print(f"  {algo}:")
                print(f"    时延范围: {min_delay:.4f} ~ {max_delay:.4f}")
                print(f"    变化幅度: {variation:.1f}%")
                print(f"    变异系数: {cv:.1f}%")
                
                # 检查是否还有直线问题
                unique_values = len(set([round(d, 4) for d in delays]))
                if unique_values <= 2:
                    print(f"    ⚠ 警告: 仍有直线问题 (只有{unique_values}个不同值)")
                elif variation < 5:
                    print(f"    ⚠ 警告: 变化幅度太小 ({variation:.1f}%)")
                else:
                    print(f"    ✅ 敏感性良好")
    
    # 绘制图表
    plot_enhanced_sensitivity(bw_results, bs_results, rs_results, bandwidth_ranges, block_sizes, rs_params)
    
    return bw_results, bs_results, rs_results

def plot_enhanced_sensitivity(bw_results, bs_results, rs_results, bandwidth_ranges, block_sizes, rs_params):
    """绘制增强的敏感性测试结果"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('增强的参数敏感性测试结果', fontsize=16)
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 'srpt算法']
    colors = ['#e377c2', '#ff7f0e', '#9467bd', '#d62728']
    
    # 1. 带宽敏感性
    ax1 = axes[0]
    bw_labels = [f"{min_bw}-{max_bw}" for min_bw, max_bw in bandwidth_ranges]
    
    for i, algo in enumerate(algorithms):
        delays = bw_results[algo]
        ax1.plot(range(len(bw_labels)), delays, 'o-', label=algo, color=colors[i], linewidth=2, markersize=6)
    
    ax1.set_title('带宽范围敏感性', fontsize=14)
    ax1.set_xlabel('带宽范围 (Mbps)')
    ax1.set_ylabel('时延')
    ax1.set_xticks(range(len(bw_labels)))
    ax1.set_xticklabels(bw_labels, rotation=45)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 块大小敏感性
    ax2 = axes[1]
    
    for i, algo in enumerate(algorithms):
        delays = bs_results[algo]
        ax2.plot(block_sizes, delays, 'o-', label=algo, color=colors[i], linewidth=2, markersize=6)
    
    ax2.set_title('块大小敏感性', fontsize=14)
    ax2.set_xlabel('块大小 (MB)')
    ax2.set_ylabel('时延')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. RS码参数敏感性
    ax3 = axes[2]
    rs_labels = [f"({n},{k})" for n, k in rs_params]
    
    for i, algo in enumerate(algorithms):
        delays = rs_results[algo]
        ax3.plot(range(len(rs_labels)), delays, 'o-', label=algo, color=colors[i], linewidth=2, markersize=6)
    
    ax3.set_title('RS码参数敏感性', fontsize=14)
    ax3.set_xlabel('RS码参数 (n,k)')
    ax3.set_ylabel('时延')
    ax3.set_xticks(range(len(rs_labels)))
    ax3.set_xticklabels(rs_labels, rotation=45)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('results/enhanced_sensitivity_test.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 增强敏感性测试图已保存: results/enhanced_sensitivity_test.png")

def check_for_flat_lines(results, test_name):
    """检查是否还有直线问题"""
    print(f"\n【{test_name}直线检查】:")
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 'srpt算法']
    
    for algo in algorithms:
        delays = results[algo]
        unique_values = len(set([round(d, 4) for d in delays]))
        total_values = len(delays)
        
        if unique_values <= 2:
            print(f"  ❌ {algo}: 严重直线问题 (只有{unique_values}/{total_values}个不同值)")
        elif unique_values <= total_values // 2:
            print(f"  ⚠ {algo}: 轻微直线问题 (只有{unique_values}/{total_values}个不同值)")
        else:
            print(f"  ✅ {algo}: 无直线问题 ({unique_values}/{total_values}个不同值)")

if __name__ == "__main__":
    try:
        bw_results, bs_results, rs_results = test_all_parameter_sensitivity()
        
        # 检查直线问题
        check_for_flat_lines(bw_results, "带宽范围测试")
        check_for_flat_lines(bs_results, "块大小测试")
        check_for_flat_lines(rs_results, "RS码参数测试")
        
        print("\n🎉 增强的参数敏感性测试完成！")
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
