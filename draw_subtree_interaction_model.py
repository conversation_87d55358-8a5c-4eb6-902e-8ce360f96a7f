#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绘制考虑子树相互制衡的修复树模型图
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch, Arc
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def draw_subtree_interaction_model():
    """绘制考虑子树相互制衡的修复树模型图"""
    
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 12)
    ax.axis('off')
    
    # 标题
    ax.text(8, 11.5, '考虑子树相互制衡的流水线化修复树模型', 
            fontsize=20, fontweight='bold', ha='center')
    
    # 颜色定义
    colors = {
        'root': '#4CAF50',           # 绿色 - 根节点
        'subtree': '#2196F3',        # 蓝色 - 子树节点
        'data_slice': '#FF9800',     # 橙色 - 数据切片
        'data_block': '#9C27B0',     # 紫色 - 数据块
        'interaction': '#F44336',    # 红色 - 相互影响
        'coordination': '#00BCD4',   # 青色 - 协调机制
        'pipeline': '#8BC34A'        # 浅绿 - 流水线
    }
    
    # 1. 绘制修复树根节点
    root_box = FancyBboxPatch((7, 10), 2, 0.6, 
                             boxstyle="round,pad=0.1", 
                             facecolor=colors['root'], 
                             edgecolor='black', linewidth=2)
    ax.add_patch(root_box)
    ax.text(8, 10.3, 'T', fontsize=16, fontweight='bold', ha='center', va='center')
    ax.text(8, 9.7, '修复树', fontsize=10, ha='center', va='center')
    
    # 2. 绘制第一层子树节点 (T1, T2, T3, T4)
    subtree_positions = [(3, 8.5), (6, 8.5), (10, 8.5), (13, 8.5)]
    subtree_labels = ['T₁', 'T₂', 'T₃', 'T₄']
    
    for i, ((x, y), label) in enumerate(zip(subtree_positions, subtree_labels)):
        # 子树节点
        subtree_box = FancyBboxPatch((x-0.4, y-0.3), 0.8, 0.6, 
                                   boxstyle="round,pad=0.05", 
                                   facecolor=colors['subtree'], 
                                   edgecolor='black', linewidth=1.5)
        ax.add_patch(subtree_box)
        ax.text(x, y, label, fontsize=12, fontweight='bold', ha='center', va='center', color='white')
        
        # 连接到根节点
        ax.plot([8, x], [10, y+0.3], 'k-', linewidth=1.5)
    
    # 3. 绘制第二层修复子树节点 (M1, M2, ..., Mr)
    m_positions = [(2, 6.5), (4, 6.5), (5.5, 6.5), (7.5, 6.5), (8.5, 6.5), (10.5, 6.5), (11.5, 6.5), (13.5, 6.5)]
    m_labels = ['M₁', 'M₂', '...', 'Mᵢ', 'Mⱼ', 'Mₖ', '...', 'Mᵣ']
    
    for i, ((x, y), label) in enumerate(zip(m_positions, m_labels)):
        if label == '...':
            ax.text(x, y, label, fontsize=12, ha='center', va='center', style='italic')
        else:
            m_box = FancyBboxPatch((x-0.3, y-0.25), 0.6, 0.5, 
                                 boxstyle="round,pad=0.05", 
                                 facecolor=colors['subtree'], 
                                 edgecolor='black', linewidth=1)
            ax.add_patch(m_box)
            ax.text(x, y, label, fontsize=10, fontweight='bold', ha='center', va='center', color='white')
        
        # 连接到上层节点
        if i < 2:  # M1, M2 连接到 T1
            ax.plot([3, x], [8.2, y+0.25], 'k-', linewidth=1)
        elif i == 2:  # ... 连接到 T1
            continue
        elif i < 5:  # Mi, Mj 连接到 T2
            ax.plot([6, x], [8.2, y+0.25], 'k-', linewidth=1)
        elif i == 5:  # Mk 连接到 T3
            ax.plot([10, x], [8.2, y+0.25], 'k-', linewidth=1)
        elif i == 6:  # ... 连接到 T3
            continue
        else:  # Mr 连接到 T4
            ax.plot([13, x], [8.2, y+0.25], 'k-', linewidth=1)
    
    # 4. 绘制第三层节点 (m1, m2, ..., mk)
    leaf_positions = [(1.5, 4.5), (2.5, 4.5), (3.5, 4.5), (4.5, 4.5), (7, 4.5), (8, 4.5), (10, 4.5), (11, 4.5), (13, 4.5), (14, 4.5)]
    leaf_labels = ['m₁', 'm₂', 'm₃', 'm₄', 'mᵢ', 'mⱼ', 'mₖ', 'mₗ', 'mₘ', 'mₙ']
    
    for i, ((x, y), label) in enumerate(zip(leaf_positions, leaf_labels)):
        leaf_box = FancyBboxPatch((x-0.25, y-0.2), 0.5, 0.4, 
                                boxstyle="round,pad=0.03", 
                                facecolor=colors['subtree'], 
                                edgecolor='black', linewidth=0.8)
        ax.add_patch(leaf_box)
        ax.text(x, y, label, fontsize=9, fontweight='bold', ha='center', va='center', color='white')
        
        # 连接到上层M节点
        if i < 2:  # m1, m2 连接到 M1
            ax.plot([2, x], [6.25, y+0.2], 'k-', linewidth=0.8)
        elif i < 4:  # m3, m4 连接到 M2
            ax.plot([4, x], [6.25, y+0.2], 'k-', linewidth=0.8)
        elif i < 6:  # mi, mj 连接到 Mi
            ax.plot([7.5, x], [6.25, y+0.2], 'k-', linewidth=0.8)
        elif i < 8:  # mk, ml 连接到 Mk
            ax.plot([10.5, x], [6.25, y+0.2], 'k-', linewidth=0.8)
        else:  # mm, mn 连接到 Mr
            ax.plot([13.5, x], [6.25, y+0.2], 'k-', linewidth=0.8)
    
    # 5. 绘制数据切片层
    slice_y = 3
    slice_positions = [(1.5, slice_y), (2.5, slice_y), (3.5, slice_y), (4.5, slice_y), 
                      (7, slice_y), (8, slice_y), (10, slice_y), (11, slice_y), 
                      (13, slice_y), (14, slice_y)]
    
    for i, (x, y) in enumerate(slice_positions):
        # 数据切片框
        slice_box = FancyBboxPatch((x-0.3, y-0.3), 0.6, 0.6, 
                                 boxstyle="round,pad=0.05", 
                                 facecolor=colors['data_slice'], 
                                 edgecolor='black', linewidth=1)
        ax.add_patch(slice_box)
        
        # 切片内的小块
        for j in range(4):
            small_x = x - 0.15 + (j % 2) * 0.15
            small_y = y - 0.1 + (j // 2) * 0.15
            small_box = patches.Rectangle((small_x, small_y), 0.1, 0.1, 
                                        facecolor='white', edgecolor='black', linewidth=0.5)
            ax.add_patch(small_box)
        
        # 标签
        ax.text(x, y-0.5, f's₁s₂s₃s₄', fontsize=7, ha='center', va='center')
        
        # 连接到上层节点
        ax.plot([leaf_positions[i][0], x], [leaf_positions[i][1]-0.2, y+0.3], 'k-', linewidth=0.8)
    
    # 6. 绘制数据块层
    block_y = 1.5
    block_positions = [(1.5, block_y), (2.5, block_y), (3.5, block_y), (4.5, block_y), 
                      (7, block_y), (8, block_y), (10, block_y), (11, block_y), 
                      (13, block_y), (14, block_y)]
    block_labels = ['N₁', 'N₂', 'N₃', 'N₄', 'Nᵢ', 'Nⱼ', 'Nₖ', 'Nₗ', 'Nₘ', 'Nₙ']
    
    for i, ((x, y), label) in enumerate(zip(block_positions, block_labels)):
        block_box = FancyBboxPatch((x-0.3, y-0.2), 0.6, 0.4, 
                                 boxstyle="round,pad=0.03", 
                                 facecolor=colors['data_block'], 
                                 edgecolor='black', linewidth=1)
        ax.add_patch(block_box)
        ax.text(x, y, label, fontsize=9, fontweight='bold', ha='center', va='center', color='white')
        
        # 连接到上层切片
        ax.plot([x, x], [slice_y-0.3, y+0.2], 'k-', linewidth=0.8)
    
    # 7. 绘制相互影响和制衡机制
    
    # 7.1 资源竞争箭头 (红色)
    competition_pairs = [
        ((2, 6.5), (4, 6.5)),      # M1 和 M2 竞争
        ((7.5, 6.5), (8.5, 6.5)),  # Mi 和 Mj 竞争
        ((10.5, 6.5), (13.5, 6.5)) # Mk 和 Mr 竞争
    ]
    
    for (x1, y1), (x2, y2) in competition_pairs:
        # 双向箭头表示竞争
        arrow1 = patches.FancyArrowPatch((x1+0.3, y1), (x2-0.3, y2),
                                       arrowstyle='<->', mutation_scale=15,
                                       color=colors['interaction'], linewidth=2,
                                       linestyle='--')
        ax.add_patch(arrow1)
    
    # 7.2 负载均衡制衡 (青色弧线)
    balance_arcs = [
        ((3, 8.5), (6, 8.5)),      # T1 和 T2 负载均衡
        ((6, 8.5), (10, 8.5)),     # T2 和 T3 负载均衡
        ((10, 8.5), (13, 8.5))     # T3 和 T4 负载均衡
    ]
    
    for (x1, y1), (x2, y2) in balance_arcs:
        # 弧形箭头表示负载均衡
        mid_x = (x1 + x2) / 2
        mid_y = y1 + 0.8
        
        arc = patches.FancyArrowPatch((x1, y1+0.3), (x2, y2+0.3),
                                    arrowstyle='<->', mutation_scale=12,
                                    color=colors['coordination'], linewidth=2,
                                    connectionstyle="arc3,rad=0.3")
        ax.add_patch(arc)
    
    # 7.3 流水线协调 (浅绿色)
    pipeline_y = 7.2
    for i in range(len(subtree_positions)-1):
        x1, _ = subtree_positions[i]
        x2, _ = subtree_positions[i+1]
        
        pipeline_arrow = patches.FancyArrowPatch((x1+0.4, pipeline_y), (x2-0.4, pipeline_y),
                                               arrowstyle='->', mutation_scale=12,
                                               color=colors['pipeline'], linewidth=3)
        ax.add_patch(pipeline_arrow)
    
    # 8. 添加说明文字
    ax.text(0.5, 10.5, '修复树', fontsize=12, fontweight='bold', ha='left')
    ax.text(0.5, 8.5, '修复子树', fontsize=12, fontweight='bold', ha='left')
    ax.text(0.5, 6.5, '修复子树', fontsize=12, fontweight='bold', ha='left')
    ax.text(0.5, 4.5, '数据切片', fontsize=12, fontweight='bold', ha='left')
    ax.text(0.5, 3, 'sᵢsⱼsₖsₗ', fontsize=10, ha='left')
    ax.text(0.5, 1.5, '数据块', fontsize=12, fontweight='bold', ha='left')
    ax.text(0.5, 0.5, '负载均衡并行修复树', fontsize=12, fontweight='bold', ha='left')
    
    # 9. 添加图例
    legend_x = 0.5
    legend_y = 0.2
    
    # 相互影响图例
    ax.text(legend_x + 8, legend_y + 1.8, '子树相互影响机制:', fontsize=12, fontweight='bold')
    
    # 资源竞争
    competition_line = patches.FancyArrowPatch((legend_x + 8, legend_y + 1.4), (legend_x + 9, legend_y + 1.4),
                                             arrowstyle='<->', mutation_scale=12,
                                             color=colors['interaction'], linewidth=2, linestyle='--')
    ax.add_patch(competition_line)
    ax.text(legend_x + 9.5, legend_y + 1.4, '资源竞争', fontsize=10, va='center')
    
    # 负载均衡
    balance_line = patches.FancyArrowPatch((legend_x + 8, legend_y + 1.0), (legend_x + 9, legend_y + 1.0),
                                         arrowstyle='<->', mutation_scale=12,
                                         color=colors['coordination'], linewidth=2,
                                         connectionstyle="arc3,rad=0.2")
    ax.add_patch(balance_line)
    ax.text(legend_x + 9.5, legend_y + 1.0, '负载均衡制衡', fontsize=10, va='center')
    
    # 流水线协调
    pipeline_line = patches.FancyArrowPatch((legend_x + 8, legend_y + 0.6), (legend_x + 9, legend_y + 0.6),
                                          arrowstyle='->', mutation_scale=12,
                                          color=colors['pipeline'], linewidth=3)
    ax.add_patch(pipeline_line)
    ax.text(legend_x + 9.5, legend_y + 0.6, '流水线协调', fontsize=10, va='center')
    
    # 10. 添加创新点标注
    innovation_box = FancyBboxPatch((12, 0.2), 3.5, 1.5, 
                                  boxstyle="round,pad=0.1", 
                                  facecolor='lightyellow', 
                                  edgecolor='orange', linewidth=2)
    ax.add_patch(innovation_box)
    ax.text(13.75, 1.3, '创新点', fontsize=12, fontweight='bold', ha='center', color='red')
    ax.text(13.75, 1.0, '• 子树间资源竞争协调', fontsize=9, ha='center')
    ax.text(13.75, 0.8, '• 动态负载均衡制衡', fontsize=9, ha='center')
    ax.text(13.75, 0.6, '• 流水线同步优化', fontsize=9, ha='center')
    ax.text(13.75, 0.4, '• 全局协调优化策略', fontsize=9, ha='center')
    
    plt.tight_layout()
    plt.savefig('results/subtree_interaction_model.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 子树相互制衡修复树模型图已保存: results/subtree_interaction_model.png")

if __name__ == "__main__":
    import os
    if not os.path.exists('results'):
        os.makedirs('results')
    
    draw_subtree_interaction_model()
    print("🎉 子树相互制衡修复树模型图绘制完成！")
