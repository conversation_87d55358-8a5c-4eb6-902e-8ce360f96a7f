import networkx as nx
import matplotlib.pyplot as plt
import random

class NetworkConfiguration:
    def __init__(self, block_size=14, bandwidth_range=(30, 300)):
        self.block_size = block_size
        self.bandwidth_range = bandwidth_range
        self.num_nodes = 41
        self.nodes = list(range(1, 41))  # 节点范围1-40
        
        # 定义拓扑结构
        self.topo_list = [
            [1, 2], [1, 6], [1, 10],
            [2, 3], [2, 6], [2, 9], [2, 10],
            [3, 4], [3, 5], [3, 7], [3, 8],
            [4, 5], [4, 7], [4, 8], [4, 10],
            [5, 8], [5, 9], [5, 10],
            [6, 7], [6, 9], [6, 11],
            [7, 8], [7, 9], [7, 12],
            [8, 10], [8, 12],
            [9, 11], [9, 13],
            [10, 13], [10, 14],
            [11, 12], [11, 15], [11, 18],
            [12, 13], [12, 16], [12, 18],
            [13, 14], [13, 17], [13, 18],
            [14, 15], [14, 18],
            [15, 16], [15, 20], [15, 22],
            [16, 17], [16, 21], [16, 22],
            [17, 18], [17, 22], [17, 23],
            [18, 24], [18, 25],
            [19, 20], [19, 22], [19, 26],
            [20, 21], [20, 23], [20, 27],
            [21, 22], [21, 23], [21, 28],
            [22, 23], [22, 28],
            [23, 24], [23, 25], [23, 29],
            [24, 25], [24, 30],
            [25, 29], [25, 31],
            [26, 27], [26, 28], [26, 32],
            [27, 28], [27, 33],
            [28, 33], [28, 34],
            [29, 30], [29, 31], [29, 35],
            [30, 31], [30, 35],
            [31, 35], [31, 36],
            [32, 33], [32, 37],
            [33, 34], [33, 37],
            [34, 37], [34, 38],
            [35, 36], [35, 39],
            [36, 39], [36, 40],
            [37, 38], [37, 40],
            [38, 40],
            [39, 40]
        ]
          # 初始化带宽矩阵
        self.bandWidth = self.generate_bandwidth_matrix()
        
        # 初始化提供者和目标节点
        self.initialize_providers_and_targets()
        
        # 确保totalproviders和targets已初始化
        if not hasattr(self, 'totalproviders'):
            self.totalproviders = [list(range(1, 40))]  # 使用1-39作为可能的提供者
        if not hasattr(self, 'targets'):
            self.targets = [{40}]  # 使用节点40作为目标节点
        
    def initialize_providers_and_targets(self):
        """初始化提供者和目标节点"""
        m = 1  # 待修复节点数
        self.totalproviders = [[] for _ in range(m)]  # 存储每个待修复节点的数据块节点集
        self.targets = [{40}]  # 使用节点40作为目标节点
        
        # 对于第一个(也是唯一的)待修复节点
        self.totalproviders[0] = list(range(1, 40))  # 使用1-39作为可能的提供者
        
    def generate_bandwidth_matrix(self):
        """生成带宽矩阵"""
        bandWidth = [[-1] * self.num_nodes for _ in range(self.num_nodes)]
        min_bw, max_bw = self.bandwidth_range
        
        for edge in self.topo_list:
            node1, node2 = edge
            if min_bw == max_bw:
                bandWidth[node1][node2] = bandWidth[node2][node1] = min_bw
            else:
                bandWidth[node1][node2] = bandWidth[node2][node1] = random.randrange(min_bw, max_bw)
        return bandWidth
    
    def update_block_size(self, size):
        """更新数据块大小"""
        self.block_size = size
        return self.block_size
    
    def update_bandwidth_range(self, min_bw, max_bw):
        """更新带宽范围并重新生成带宽矩阵"""
        self.bandwidth_range = (min_bw, max_bw)
        self.bandWidth = self.generate_bandwidth_matrix()
        return self.bandWidth
    
    def get_network_graph(self):
        """获取网络图"""
        ny_graph = nx.Graph()
        for edge in self.topo_list:
            ny_graph.add_edge(edge[0], edge[1])
            ny_graph[edge[0]][edge[1]]['bw'] = self.bandWidth[edge[0]][edge[1]]
            ny_graph[edge[0]][edge[1]]['true_weight'] = 1 / ny_graph[edge[0]][edge[1]]['bw']
        return ny_graph
    
    def plot_network(self, save_path="output.png"):
        """绘制网络拓扑图"""
        G = self.get_network_graph()
        pos = nx.spring_layout(G)
        plt.figure(figsize=(12, 8))
        nx.draw(G, pos, with_labels=True, node_color='lightblue', 
                node_size=500, arrowsize=20, font_size=12)
        labels = nx.get_edge_attributes(G,'bw')
        nx.draw_networkx_edge_labels(G, pos, edge_labels=labels)
        plt.savefig(save_path)
        plt.close()

# 为了保持向后兼容性
default_network = NetworkConfiguration()
bandWidth = default_network.bandWidth
ny_graph = default_network.get_network_graph()
nodes = default_network.nodes  # 导出节点列表
num_nodes = default_network.num_nodes  # 确保导出节点数量
topo_list = default_network.topo_list  # 导出拓扑列表
blockSize = default_network.block_size  # 导出块大小
