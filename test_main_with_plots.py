#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Main.py的图表生成功能
"""

import os
import time
import matplotlib.pyplot as plt
import numpy as np
from standardTopo import NetworkConfiguration
from WOA import WhaleOptimizationAlgorithm
from PopelineWOA import PipelinedWOA
from EnhancedPipelinedWOA import EnhancedPipelinedWOA
import GAsye
from aggre_tree import AggreTree
from SRPT import SRPT

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def plot_basic_comparison(results, n, k):
    """绘制基础算法性能对比图"""
    # 准备数据
    algorithms = list(results.keys())
    metrics = {
        'transmission_delay': '时延',
        'flow_consumption': '流量消耗',
        'std_deviation': '负载均衡'
    }
    
    # 设置颜色
    colors = {
        'ye_opt算法': '#1f77b4',
        'aggre算法': '#2ca02c',
        'srpt算法': '#d62728',
        'WOA算法': '#9467bd',
        'PipelinedWOA算法': '#ff7f0e',
        'EnhancedPipelinedWOA算法': '#e377c2'
    }
    
    # 创建子图
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle(f'算法性能对比 (n={n}, k={k})', fontsize=16)
    
    for idx, (metric_key, metric_name) in enumerate(metrics.items()):
        ax = axes[idx]
        
        # 提取数据
        values = []
        labels = []
        bar_colors = []
        
        for algo in algorithms:
            values.append(results[algo][metric_key])
            labels.append(algo)
            bar_colors.append(colors.get(algo, '#333333'))
        
        # 绘制柱状图
        bars = ax.bar(range(len(algorithms)), values, color=bar_colors, alpha=0.8)
        
        # 设置标签和标题
        ax.set_title(metric_name, fontsize=14)
        ax.set_ylabel(metric_name, fontsize=12)
        ax.set_xticks(range(len(algorithms)))
        ax.set_xticklabels(labels, rotation=45, ha='right')
        
        # 添加数值标签
        for i, (bar, value) in enumerate(zip(bars, values)):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{value:.4f}', ha='center', va='bottom', fontsize=10)
        
        # 添加网格
        ax.grid(True, alpha=0.3, axis='y')
        
        # 特别标注WOA系列算法
        for i, algo in enumerate(algorithms):
            if 'WOA' in algo:
                bars[i].set_edgecolor('black')
                bars[i].set_linewidth(2)
    
    plt.tight_layout()
    plt.savefig(f'results/basic_comparison_n{n}_k{k}.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 生成性能排序图
    plot_performance_ranking(results, n, k)

def plot_performance_ranking(results, n, k):
    """绘制性能排序图"""
    # 按时延排序
    sorted_results = sorted(results.items(), key=lambda x: x[1]['transmission_delay'])
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    algorithms = [item[0] for item in sorted_results]
    delays = [item[1]['transmission_delay'] for item in sorted_results]
    
    # 设置颜色
    colors = ['#e377c2', '#ff7f0e', '#9467bd', '#d62728', '#1f77b4', '#2ca02c']
    
    # 创建柱状图
    x_pos = range(len(algorithms))
    bars = ax.bar(x_pos, delays, color=colors[:len(algorithms)], alpha=0.8)
    
    # 设置标签
    ax.set_title(f'算法时延性能排序 (n={n}, k={k})', fontsize=16)
    ax.set_ylabel('时延', fontsize=14)
    ax.set_xticks(x_pos)
    ax.set_xticklabels(algorithms, rotation=45, ha='right')
    
    # 添加数值标签
    for i, (bar, delay) in enumerate(zip(bars, delays)):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height,
               f'{delay:.4f}', ha='center', va='bottom', fontsize=11, fontweight='bold')
    
    # 特别标注前三名
    for i in range(min(3, len(bars))):
        bars[i].set_edgecolor('gold')
        bars[i].set_linewidth(3)
        # 添加排名标注
        ax.text(i, delays[i] + max(delays) * 0.05, f'第{i+1}名', 
               ha='center', va='bottom', fontsize=12, fontweight='bold', color='red')
    
    ax.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig(f'results/performance_ranking_n{n}_k{k}.png', dpi=300, bbox_inches='tight')
    plt.close()

def run_all_algorithms(network_config, n, k):
    """运行所有算法并返回结果"""
    results = {}
    
    print(f"\n开始运行所有算法对比 (n={n}, k={k})...")
    
    try:
        # 运行各个算法
        print("运行 ye_opt算法...")
        ye_opt = GAsye.GeneticAlgorithm_ye()
        results['ye_opt算法'] = ye_opt.run(n, k, network_config)
        
        print("运行 aggre算法...")
        results['aggre算法'] = AggreTree.run(n, k, network_config)
        
        print("运行 srpt算法...")
        srpt_algo = SRPT()
        results['srpt算法'] = srpt_algo.run(n, k, network_config)
        
        print("运行 WOA算法...")
        woa = WhaleOptimizationAlgorithm(nwhales=15, max_iter=25)
        woa_result = woa.run(n, k, network_config, return_iterations=False)
        results['WOA算法'] = woa_result
        
        print("运行 PipelinedWOA算法...")
        pwoa_result = PipelinedWOA.run(n, k, network_config, return_iterations=False)
        results['PipelinedWOA算法'] = pwoa_result
        
        print("运行 EnhancedPipelinedWOA算法...")
        epwoa_result = EnhancedPipelinedWOA.run(n, k, network_config, return_iterations=False)
        results['EnhancedPipelinedWOA算法'] = epwoa_result
        
    except Exception as e:
        print(f"运行算法时出错: {str(e)}")
        raise
    
    return results

def test_main_with_plots():
    """测试Main.py的图表生成功能"""
    # 创建results目录（如果不存在）
    if not os.path.exists('results'):
        os.makedirs('results')
    
    print("=" * 80)
    print("测试Main.py的图表生成功能")
    print("=" * 80)
    start_time = time.time()
    
    try:
        # 运行基础算法对比
        print("\n【第一步】运行基础算法性能对比...")
        network_config = NetworkConfiguration()
        n, k = 20, 40
        
        print(f"网络配置信息:")
        print(f"  节点数: {len(network_config.nodes)}")
        ny_graph = network_config.get_network_graph()
        print(f"  边数: {len(ny_graph.edges())}")
        print(f"  块大小: {network_config.block_size}")
        
        # 运行所有算法
        results = run_all_algorithms(network_config, n, k)
        
        # 显示结果
        print("\n" + "=" * 60)
        print("算法性能对比结果:")
        print("=" * 60)
        
        # 按时延排序显示
        sorted_results = sorted(results.items(), key=lambda x: x[1]['transmission_delay'])
        
        for i, (algo_name, result) in enumerate(sorted_results, 1):
            print(f"{i}. {algo_name}:")
            print(f"   时延: {result['transmission_delay']:.4f}")
            print(f"   流量: {result['flow_consumption']:.4f}")
            print(f"   负载均衡: {result['std_deviation']:.4f}")
        
        # 生成图表
        print(f"\n【第二步】生成图表...")
        try:
            plot_basic_comparison(results, n, k)
            print("✅ 基础对比图表已保存到 results 目录")
            print(f"   - results/basic_comparison_n{n}_k{k}.png")
            print(f"   - results/performance_ranking_n{n}_k{k}.png")
        except Exception as e:
            print(f"⚠ 生成图表时出错: {str(e)}")
        
        # 计算总运行时间
        end_time = time.time()
        total_time = end_time - start_time
        print(f"\n" + "=" * 80)
        print(f"测试完成！")
        print(f"总运行时间: {total_time:.2f}秒 ({total_time/60:.2f}分钟)")
        print("=" * 80)
        
        return results
        
    except Exception as e:
        print(f"测试运行出错: {str(e)}")
        raise

if __name__ == "__main__":
    try:
        results = test_main_with_plots()
        print("\n🎉 测试成功！图表已生成到 results 目录")
    except KeyboardInterrupt:
        print("\n\n用户中断了测试")
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
