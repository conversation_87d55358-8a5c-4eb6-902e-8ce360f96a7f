# -*- coding: utf-8 -*-
import time
import random
import ctypes
import collections
import numpy as np
import copy
import math
from deap import base, creator, tools, algorithms
import heapq
from standardTopo import bandWidth, blockSize, ny_graph, topo_list, num_nodes
import networkx as nx
import networkx.algorithms.approximation as nx_approx
import matplotlib.pyplot as pyplot
import statistics

class GeneticAlgorithm_ye:
    def __init__(self):
        random.seed(2)
        self.totalproviders = None 
        self.k = None
        self.targets = None
        self.create_deap_classes()
        self.toolbox = base.Toolbox()
        self.setup_deap_toolbox()

    def create_deap_classes(self):
        # 如果 FitnessMulti 已经存在，先删除它
        if hasattr(creator, 'FitnessMult<PERSON>'):
            delattr(creator, 'FitnessMult<PERSON>')
        if hasattr(creator, 'Individual'):
            delattr(creator, 'Individual')
            
        creator.create("FitnessMulti", base.Fitness, weights=(0.00001,0.00001,0.00001,1.0))
        creator.create("Individual", list, fitness=creator.Fitness<PERSON>ulti, chromosome=None)

    def calcpsdv(self, G, topo_list):
        weight_list = [100 * (bandWidth[edge[0]][edge[1]] - G[edge[0]][edge[1]]['bw']) / bandWidth[edge[0]][edge[1]] for edge in topo_list]
        return statistics.pstdev(weight_list)

    def create_individual(self):
        individual = creator.Individual()
        individual.chromosome = [[] for _ in range(len(self.targets))]
        for index, target in enumerate(self.targets):
            snodes = [random.choice(list(target))]  # 根节点是当前目标和新节点随机选取
            available_providers = list(set(self.totalproviders[index]) - set(snodes))  # 获取对应目标的可用providers

            onetree_chromosome = [[] for _ in range(self.k)]
            for i in range(self.k):
                selected_provider = random.choice(available_providers)
                available_providers.remove(selected_provider)

                onetree_chromosome[i].append(selected_provider)
                snodes.append(selected_provider)

            T = nx_approx.steiner_tree(ny_graph, snodes, weight='true_weight', method='kou')
            for j in range(self.k):
                temppathk = nx.dijkstra_path(T, onetree_chromosome[j][0], snodes[0])
                if not temppathk:
                    temppathk = random.choice(list(nx.all_shortest_paths(T, onetree_chromosome[j][0], target)))
                onetree_chromosome[j] = temppathk

            individual.chromosome[index] = onetree_chromosome

        individual.fitness.values = self.evaluate(individual)
        return individual

    def evaluate(self, individual):
        G_tree = copy.deepcopy(ny_graph)
        min_bandwidth_individual = float('inf')  # 初始化为正无穷大
        
        for r in range(len(individual.chromosome)):
            for path in individual.chromosome[r]:
                min_bandwidth_path = float('inf')  # 初始化为正无穷大
                for j in range(len(path) - 1):
                    source = path[j]
                    destination = path[j + 1]
                    bw = G_tree[source][destination]['bw']
                    if bw < min_bandwidth_path:
                        min_bandwidth_path = bw
                if min_bandwidth_path < min_bandwidth_individual:
                    min_bandwidth_individual = min_bandwidth_path

        individual.fitness.values = (1, 1, 1, min_bandwidth_individual)
        return individual.fitness.values

    def setup_deap_toolbox(self):
        """配置DEAP工具箱，设置遗传算法操作"""
        # 注册个体创建方法
        self.toolbox.register("individual", self.create_individual)
        self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)
        
        # 注册遗传操作
        self.toolbox.register("evaluate", self.evaluate)
        self.toolbox.register("mate", tools.cxTwoPoint)
        self.toolbox.register("mutate", tools.mutFlipBit, indpb=0.05)
        self.toolbox.register("select", tools.selTournament, tournsize=3)

    def run(self, n, k, network_config):
        """运行遗传算法优化过程"""
        # 设置参数
        self.k = k
        self.totalproviders = network_config.totalproviders
        self.targets = network_config.targets
        
        # 遗传算法参数
        NGEN = 100  # 迭代次数
        MU = 50    # 种群大小
        LAMBDA = 100  # 子代大小
        CXPB = 0.7  # 交叉概率
        MUTPB = 0.3  # 变异概率
        
        # 初始化种群
        pop = [self.create_individual() for _ in range(MU)]
        hof = tools.ParetoFront()
        
        # 设置统计
        stats = tools.Statistics(lambda ind: ind.fitness.values)
        stats.register("avg", np.mean, axis=0)
        stats.register("std", np.std, axis=0)
        stats.register("min", np.min, axis=0)
        stats.register("max", np.max, axis=0)
        
        # 运行算法
        algorithms.eaSimple(pop, self.toolbox, CXPB, MUTPB, NGEN, stats, halloffame=hof, verbose=False)
        
        # 获取最优解
        if len(hof) > 0:
            best_individual = hof[0]
            return {
                'transmission_delay': best_individual.fitness.values[0],
                'flow_consumption': network_config.block_size * best_individual.fitness.values[1],
                'std_deviation': best_individual.fitness.values[2]
            }
        else:
            return {
                'transmission_delay': float('inf'),
                'flow_consumption': float('inf'),
                'std_deviation': float('inf')
            }
