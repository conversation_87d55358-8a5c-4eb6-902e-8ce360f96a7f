#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证测试 - 确认所有问题都已修复
验证：
1. WOA算法时延 < srpt算法时延 ✓
2. PipelinedWOA算法能够成功运行 ✓
3. 性能排序：EnhancedPipelinedWOA > PipelinedWOA > WOA > 其他算法
"""

import time
import sys
import threading
from standardTopo import NetworkConfiguration
from WOA import WhaleOptimizationAlgorithm
from PopelineWOA import PipelinedWOA
from EnhancedPipelinedWOA import EnhancedPipelinedWOA
import GAsye
from aggre_tree import AggreTree
from SRPT import SRPT

def run_with_timeout(func, timeout_seconds=120):
    """带超时机制运行函数"""
    result = [None]
    exception = [None]
    
    def target():
        try:
            result[0] = func()
        except Exception as e:
            exception[0] = e
    
    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout_seconds)
    
    if thread.is_alive():
        print(f"算法运行超时 ({timeout_seconds}秒)")
        return None
    elif exception[0] is not None:
        print(f"算法运行出错: {str(exception[0])}")
        return None
    else:
        return result[0]

def test_algorithm_final(algo_name, n, k, network_config):
    """最终测试单个算法"""
    print(f"\n开始测试 {algo_name}...")
    print(f"参数: n={n}, k={k}")
    
    start_time = time.time()
    
    def run_algo():
        if algo_name == 'WOA算法':
            print("  调用 WhaleOptimizationAlgorithm...")
            woa = WhaleOptimizationAlgorithm(nwhales=15, max_iter=25)
            return woa.run(n, k, network_config)
        elif algo_name == 'PipelinedWOA算法':
            print("  调用 PipelinedWOA.run...")
            return PipelinedWOA.run(n, k, network_config)
        elif algo_name == 'EnhancedPipelinedWOA算法':
            print("  调用 EnhancedPipelinedWOA.run...")
            return EnhancedPipelinedWOA.run(n, k, network_config)
        elif algo_name == 'ye_opt算法':
            print("  调用 GAsye.run...")
            ga = GAsye.GeneticAlgorithm_ye()
            return ga.run(n, k, network_config)
        elif algo_name == 'aggre算法':
            print("  调用 AggreTree.run...")
            return AggreTree.run(n, k, network_config)
        elif algo_name == 'srpt算法':
            print("  调用 SRPT.run...")
            return SRPT.run(n, k, network_config)
        else:
            raise ValueError(f"未知算法: {algo_name}")
    
    # 设置超时时间
    timeout_map = {
        'WOA算法': 90,
        'PipelinedWOA算法': 60,
        'EnhancedPipelinedWOA算法': 60,
        'ye_opt算法': 120,
        'aggre算法': 60,
        'srpt算法': 60
    }
    
    timeout = timeout_map.get(algo_name, 90)
    result = run_with_timeout(run_algo, timeout_seconds=timeout)
    
    end_time = time.time()
    runtime = end_time - start_time
    
    if result is not None:
        print(f"  ✓ 算法运行成功")
        print(f"  运行时间: {runtime:.2f}秒")
        print(f"  时延: {result['transmission_delay']:.4f}")
        print(f"  流量: {result['flow_consumption']:.4f}")
        print(f"  负载均衡: {result['std_deviation']:.4f}")
        return {
            'success': True,
            'runtime': runtime,
            'result': result
        }
    else:
        print(f"  ✗ 算法运行失败")
        print(f"  运行时间: {runtime:.2f}秒")
        return {
            'success': False,
            'runtime': runtime,
            'result': None
        }

def final_validation_test():
    """最终验证测试"""
    print("=" * 80)
    print("最终验证测试 - 确认所有问题都已修复")
    print("=" * 80)
    
    # 使用合适的参数
    n, k = 20, 40
    network_config = NetworkConfiguration()
    
    print(f"网络配置信息:")
    print(f"  节点数: {len(network_config.nodes)}")
    ny_graph = network_config.get_network_graph()
    print(f"  边数: {len(ny_graph.edges())}")
    print(f"  块大小: {network_config.block_size}")
    
    # 按照预期性能排序的算法列表
    algorithms = [
        'EnhancedPipelinedWOA算法',  # 应该是最优的
        'PipelinedWOA算法',          # 应该是次优的
        'WOA算法',                   # 应该是第三
        'ye_opt算法',               # 其他算法
        'aggre算法', 
        'srpt算法'
    ]
    
    results = {}
    total_start_time = time.time()
    
    for algo_name in algorithms:
        try:
            result = test_algorithm_final(algo_name, n, k, network_config)
            results[algo_name] = result
            
            print("  等待3秒后继续...")
            time.sleep(3)
            
        except KeyboardInterrupt:
            print(f"\n用户中断了 {algo_name} 的测试")
            break
        except Exception as e:
            print(f"\n测试 {algo_name} 时发生未预期错误: {str(e)}")
            results[algo_name] = {
                'success': False,
                'runtime': 0,
                'result': None
            }
    
    total_end_time = time.time()
    total_runtime = total_end_time - total_start_time
    
    print("\n" + "=" * 80)
    print("最终验证结果:")
    print("=" * 80)
    
    # 显示所有结果
    successful_results = {}
    for algo_name, result in results.items():
        if result['success']:
            print(f"✓ {algo_name}: 成功 ({result['runtime']:.2f}秒)")
            print(f"    时延: {result['result']['transmission_delay']:.4f}")
            print(f"    流量: {result['result']['flow_consumption']:.4f}")
            print(f"    负载均衡: {result['result']['std_deviation']:.4f}")
            successful_results[algo_name] = result['result']
        else:
            print(f"✗ {algo_name}: 失败 ({result['runtime']:.2f}秒)")
    
    print(f"\n总运行时间: {total_runtime:.2f}秒 ({total_runtime/60:.2f}分钟)")
    
    # 最终验证
    print("\n" + "=" * 80)
    print("关键问题验证:")
    print("=" * 80)
    
    validation_passed = True
    
    if successful_results:
        print("\n时延性能排序（从低到高）:")
        sorted_by_delay = sorted(successful_results.items(), key=lambda x: x[1]['transmission_delay'])
        for i, (name, result) in enumerate(sorted_by_delay, 1):
            delay = result['transmission_delay']
            print(f"  {i}. {name}: {delay:.4f}")
        
        # 验证问题1：WOA vs srpt
        print("\n【问题1验证】WOA算法时延应该 < srpt算法时延:")
        if 'WOA算法' in successful_results and 'srpt算法' in successful_results:
            woa_delay = successful_results['WOA算法']['transmission_delay']
            srpt_delay = successful_results['srpt算法']['transmission_delay']
            if woa_delay < srpt_delay:
                improvement = ((srpt_delay - woa_delay) / srpt_delay) * 100
                print(f"✅ WOA算法时延 ({woa_delay:.4f}) < srpt算法时延 ({srpt_delay:.4f})")
                print(f"   WOA比srpt改善了 {improvement:.1f}%")
            else:
                print(f"❌ WOA算法时延 ({woa_delay:.4f}) >= srpt算法时延 ({srpt_delay:.4f})")
                validation_passed = False
        else:
            print("❌ WOA或srpt算法未成功运行，无法验证")
            validation_passed = False
        
        # 验证问题2：PipelinedWOA能否运行
        print("\n【问题2验证】PipelinedWOA算法应该能够成功运行:")
        if 'PipelinedWOA算法' in successful_results:
            print(f"✅ PipelinedWOA算法成功运行")
            pwoa_delay = successful_results['PipelinedWOA算法']['transmission_delay']
            
            # 验证PipelinedWOA vs WOA
            if 'WOA算法' in successful_results:
                woa_delay = successful_results['WOA算法']['transmission_delay']
                if pwoa_delay < woa_delay:
                    improvement = ((woa_delay - pwoa_delay) / woa_delay) * 100
                    print(f"✅ PipelinedWOA时延 ({pwoa_delay:.4f}) < WOA时延 ({woa_delay:.4f})")
                    print(f"   PipelinedWOA比WOA改善了 {improvement:.1f}%")
                else:
                    print(f"⚠ PipelinedWOA时延 ({pwoa_delay:.4f}) >= WOA时延 ({woa_delay:.4f})")
        else:
            print(f"❌ PipelinedWOA算法仍然无法运行")
            validation_passed = False
        
        # 验证预期性能排序
        print("\n【性能排序验证】预期：EnhancedPipelinedWOA > PipelinedWOA > WOA > 其他:")
        woa_series = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法']
        available_woa = [algo for algo in woa_series if algo in successful_results]
        
        if len(available_woa) >= 2:
            print("WOA系列算法时延对比:")
            for i, algo in enumerate(available_woa):
                delay = successful_results[algo]['transmission_delay']
                print(f"  {algo}: {delay:.4f}")
                if i > 0:
                    prev_algo = available_woa[i-1]
                    prev_delay = successful_results[prev_algo]['transmission_delay']
                    if delay > prev_delay:
                        improvement = ((delay - prev_delay) / prev_delay) * 100
                        print(f"    ✅ 比{prev_algo}性能下降 {improvement:.1f}%（符合预期）")
                    else:
                        print(f"    ❌ 比{prev_algo}性能更优（不符合预期）")
                        validation_passed = False
        
        # 验证EnhancedPipelinedWOA是否最优
        if 'EnhancedPipelinedWOA算法' in successful_results:
            enhanced_delay = successful_results['EnhancedPipelinedWOA算法']['transmission_delay']
            better_count = 0
            total_count = 0
            for name, result in successful_results.items():
                if name != 'EnhancedPipelinedWOA算法':
                    total_count += 1
                    if enhanced_delay <= result['transmission_delay']:
                        better_count += 1
            
            print(f"\n【最优性验证】EnhancedPipelinedWOA在时延方面优于 {better_count}/{total_count} 个算法")
            if better_count == total_count:
                print(f"✅ EnhancedPipelinedWOA算法在时延方面优于所有其他算法")
            else:
                print(f"⚠ EnhancedPipelinedWOA算法未在时延方面优于所有其他算法")
    
    print("\n" + "=" * 80)
    if validation_passed:
        print("🎉 所有关键问题都已成功修复！")
        print("✅ 1. WOA算法时延 < srpt算法时延")
        print("✅ 2. PipelinedWOA算法能够成功运行")
        print("✅ 3. 性能排序符合预期")
    else:
        print("⚠ 部分问题仍需进一步调优")
    print("=" * 80)
    
    return results, validation_passed

if __name__ == "__main__":
    try:
        results, validation_passed = final_validation_test()
        print("\n最终验证测试完成！")
        if validation_passed:
            print("🎊 恭喜！所有问题都已成功解决！")
        else:
            print("🔧 部分问题需要进一步优化")
    except KeyboardInterrupt:
        print("\n\n用户中断了测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
        sys.exit(1)
