import random
import matplotlib.pyplot as plt
import networkx as nx
import copy
import time  
import statistics
import numpy as np
import threading
import os
from standardTopo import (
    bandWidth, nodes, blockSize, ny_graph, topo_list, 
    num_nodes, NetworkConfiguration
)
import networkx.algorithms.approximation as nx_approx
import GAsye  # 遗传算法模块
from aggre_tree import AggreTree, TreeEvaluator  # 修改导入语句
from SRPT import SRPT  # 修改导入方式，不再导入函数而是导入类
import sys
from WOA import WhaleOptimizationAlgorithm  
from PopelineWOA import PipelinedWOA
from EnhancedPipelinedWOA import EnhancedPipelinedWOA
import seaborn as sns
import pandas as pd
from experiment_analysis import (
    run_rs_code_comparison,
    run_bandwidth_comparison,
    run_block_size_comparison
)

plt.rcParams['font.sans-serif']=['SimHei']
plt.rcParams['axes.unicode_minus']=False
# 这两行是设置matplotlib的参数使得图表可以显示中文和负号


def plot_comparison(results, n, k):
    """绘制算法性能对比图"""
    # 准备数据
    algorithms = list(results.keys())
    metrics = ['时延', '流量', '负载均衡']
    data = {
        '算法': [],
        '指标': [],
        '值': []
    }
    
    for algo in algorithms:
        for metric in metrics:
            data['算法'].extend([algo] * len(results[algo][metric.replace('时延', 'transmission_delay').replace('流量', 'flow_consumption').replace('负载均衡', 'std_deviation')]))
            data['指标'].extend([metric] * len(results[algo][metric.replace('时延', 'transmission_delay').replace('流量', 'flow_consumption').replace('负载均衡', 'std_deviation')]))
            data['值'].extend(results[algo][metric.replace('时延', 'transmission_delay').replace('流量', 'flow_consumption').replace('负载均衡', 'std_deviation')])
    
    df = pd.DataFrame(data)
    
    # 创建子图
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    fig.suptitle(f'算法性能对比 (n={n}, k={k})')
    
    # 绘制每个指标的箱线图
    for i, metric in enumerate(metrics):
        sns.boxplot(x='算法', y='值', data=df[df['指标'] == metric], ax=axes[i])
        axes[i].set_title(metric)
        axes[i].set_xticklabels(axes[i].get_xticklabels(), rotation=45)
        
    plt.tight_layout()
    plt.savefig(f'results/comparison_n{n}_k{k}.png')
    plt.close()

def plot_convergence(iteration_data, n, k):
    """绘制收敛曲线"""
    plt.figure(figsize=(10, 6))
    for algo, data in iteration_data.items():
        plt.plot(data['iterations'], data['fitness'], label=algo)
    
    plt.title(f'算法收敛曲线 (n={n}, k={k})')
    plt.xlabel('迭代次数')
    plt.ylabel('适应度值')
    plt.legend()
    plt.grid(True)
    plt.savefig(f'results/convergence_n{n}_k{k}.png')
    plt.close()

"""
def plot_erasure_code_comparison(all_results):
    #绘制基于纠删码参数的性能对比曲线（对数刻度版）
    # 准备数据
    erasure_params = []
    algorithms = list(next(iter(all_results.values())).keys())
    metrics = {
        'transmission_delay': '时延',
        'flow_consumption': '流量',
        'std_deviation': '负载均衡'
    }
    
    # 整理数据结构
    performance_data = {
        algo: {metric: [] for metric in metrics.keys()} 
        for algo in algorithms
    }
    
    # 收集所有(n,k)对和对应的性能数据
    for (n, k), results in all_results.items():
        erasure_params.append(f"({n},{k})")
        for algo in algorithms:
            for metric in metrics.keys():
                avg_value = np.mean(results[algo][metric])
                performance_data[algo][metric].append(avg_value)
    
    # 创建子图
    fig, axes = plt.subplots(1, 3, figsize=(18, 8))
    fig.suptitle('基于纠删码参数的算法性能对比（对数刻度）', fontsize=16, y=1.05)
    
    # 设置颜色和样式（保持原样）
    colors = {
        'ye_opt算法': '#1f77b4',
        'aggre算法': '#2ca02c',
        'srpt算法': '#d62728',
        'WOA算法': '#9467bd',
        'PipelinedWOA算法': '#ff7f0e',
        'EnhancedPipelinedWOA算法': '#e377c2'
    }
    
    line_widths = {
        'ye_opt算法': 1.5,
        'aggre算法': 1.5,
        'srpt算法': 1.5,
        'WOA算法': 2.5,
        'PipelinedWOA算法': 2.5,
        'EnhancedPipelinedWOA算法': 2.5
    }
    
    markers = {
        'ye_opt算法': 'o',
        'aggre算法': 's',
        'srpt算法': '^',
        'WOA算法': 'D',
        'PipelinedWOA算法': 'v',
        'EnhancedPipelinedWOA算法': '*'
    }
    
    marker_sizes = {
        'ye_opt算法': 7,
        'aggre算法': 7,
        'srpt算法': 7,
        'WOA算法': 8,
        'PipelinedWOA算法': 8,
        'EnhancedPipelinedWOA算法': 8
    }
    
    # 为每个性能指标绘制一个子图
    for idx, (metric, metric_name) in enumerate(metrics.items()):
        ax = axes[idx]
        
        # 获取所有算法在当前指标下的值
        all_values = []
        for algo in algorithms:
            values = performance_data[algo][metric]
            all_values.extend(values)
        
        # 计算全局范围（保护性处理0值）
        min_val = max(min(all_values), 1e-3)  # 避免0值
        max_val = max(all_values)
        
        # 设置对数刻度（关键修改部分）
        ax.set_yscale('log')
        ax.set_ylim(min_val * 0.8, max_val * 1.5)  # 对数范围
        
        # 优化对数刻度显示
        ax.yaxis.set_major_formatter(plt.ScalarFormatter())
        ax.yaxis.set_minor_formatter(plt.NullFormatter())
        ax.yaxis.set_major_locator(plt.LogLocator(base=10, numticks=15))
        
        # 绘制所有算法（保持原样）
        for algo in algorithms:
            values = performance_data[algo][metric]
            ax.plot(range(len(erasure_params)), values,
                   label=algo,
                   color=colors[algo],
                   linestyle='-',
                   marker=markers[algo],
                   linewidth=line_widths[algo],
                   markersize=marker_sizes[algo])
        
        ax.set_title(f'{metric_name}对比', fontsize=14, pad=15)
        ax.set_xlabel('纠删码参数(n,k)', fontsize=12)
        ax.set_ylabel(f'{metric_name}（对数刻度）', fontsize=12)  # 添加刻度说明
        ax.set_xticks(range(len(erasure_params)))
        ax.set_xticklabels(erasure_params, rotation=45)
        
        # 添加网格和图例（对数刻度专用网格）
        ax.grid(True, which='both', linestyle='--', alpha=0.3)
        ax.legend(bbox_to_anchor=(0.5, -0.35), loc='upper center', ncol=2)
    
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.25)
    plt.savefig('results/erasure_code_comparison_log.png', dpi=300, bbox_inches='tight')
    plt.close()
"""



#绘制基于纠删码参数的性能对比曲线
def plot_erasure_code_comparison(all_results):
    # 准备数据
    erasure_params = []
    algorithms = list(next(iter(all_results.values())).keys())
    metrics = {
        'transmission_delay': '时延',
        'flow_consumption': '流量',
        'std_deviation': '负载均衡'
    }
    
    # 设置颜色和样式
    colors = {
        'ye_opt算法': '#1f77b4',
        'aggre算法': '#2ca02c',
        'srpt算法': '#d62728',
        'WOA算法': '#9467bd',
        'PipelinedWOA算法': '#ff7f0e',
        'EnhancedPipelinedWOA算法': '#e377c2'
    }
    
    line_widths = {
        'ye_opt算法': 1.5,
        'aggre算法': 1.5,
        'srpt算法': 1.5,
        'WOA算法': 2.5,
        'PipelinedWOA算法': 2.5,
        'EnhancedPipelinedWOA算法': 2.5
    }
    
    markers = {
        'ye_opt算法': 'o',
        'aggre算法': 's',
        'srpt算法': '^',
        'WOA算法': 'D',
        'PipelinedWOA算法': 'v',
        'EnhancedPipelinedWOA算法': '*'
    }
    
    marker_sizes = {
        'ye_opt算法': 7,
        'aggre算法': 7,
        'srpt算法': 7,
        'WOA算法': 8,
        'PipelinedWOA算法': 8,
        'EnhancedPipelinedWOA算法': 8
    }
    
    # 整理数据结构
    performance_data = {
        algo: {metric: [] for metric in metrics.keys()} 
        for algo in algorithms
    }
    
    # 收集所有(n, k)对和对应的性能数据
    for (n, k), results in all_results.items():
        erasure_params.append(f"({n},{k})")
        for algo in algorithms:
            for metric in metrics.keys():
                avg_value = np.mean(results[algo][metric])
                performance_data[algo][metric].append(avg_value)
    
    # 创建子图
    fig, axes = plt.subplots(1, 3, figsize=(18, 8))
    fig.suptitle('基于纠删码参数的算法性能对比', fontsize=16, y=1.05)
    
    # 为每个性能指标绘制一个子图
    for idx, (metric, metric_name) in enumerate(metrics.items()):
        ax = axes[idx]
        
        # 获取所有算法在当前指标下的值
        all_values = []
        woa_values = []  # 专门存储WOA系列算法的值
        for algo in algorithms:
            values = performance_data[algo][metric]
            all_values.extend(values)
            if algo in ['WOA算法', 'PipelinedWOA算法', 'EnhancedPipelinedWOA算法']:
                woa_values.extend(values)
        
        # 计算全局范围和WOA系列范围
        min_val = min(all_values)
        max_val = max(all_values)
        
        # 设置y轴范围
        y_min = max(0, min_val - (max_val - min_val) * 0.1)
        y_max = max_val + (max_val - min_val) * 0.1
        
        # 绘制所有算法
        for algo in algorithms:
            values = performance_data[algo][metric]
            ax.plot(range(len(erasure_params)), values,
                   label=algo,
                   color=colors[algo],
                   linestyle='-',
                   marker=markers[algo],
                   linewidth=line_widths[algo],
                   markersize=marker_sizes[algo])
        
        ax.set_ylim(y_min, y_max)
        ax.set_title(f'{metric_name}对比', fontsize=14, pad=15)
        ax.set_xlabel('纠删码参数(n,k)', fontsize=12)
        ax.set_ylabel(metric_name, fontsize=12)
        ax.set_xticks(range(len(erasure_params)))
        ax.set_xticklabels(erasure_params, rotation=45)
        
        ax.grid(True, linestyle='--', alpha=0.3)
        ax.legend(bbox_to_anchor=(0.5, -0.35), loc='upper center', ncol=2)
    
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.25)  # 为图例留出空间
    plt.savefig('results/erasure_code_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()


"""
def plot_erasure_code_comparison(all_results):
    #绘制基于纠删码参数的性能对比曲线（增强版）
    # 准备数据
    erasure_params = []
    algorithms = list(next(iter(all_results.values())).keys())
    metrics = {
        'transmission_delay': '时延',
        'flow_consumption': '流量',
        'std_deviation': '负载均衡'
    }
    
    # 整理数据结构
    performance_data = {
        algo: {metric: [] for metric in metrics.keys()} 
        for algo in algorithms
    }
    
    # 收集所有(n,k)对和对应的性能数据
    for (n, k), results in all_results.items():
        erasure_params.append(f"({n},{k})")
        for algo in algorithms:
            for metric in metrics.keys():
                avg_value = np.mean(results[algo][metric])
                performance_data[algo][metric].append(avg_value)
    
    # 创建子图
    fig, axes = plt.subplots(1, 3, figsize=(18, 8))
    fig.suptitle('基于纠删码参数的算法性能对比', fontsize=16, y=1.05)
    
    # 设置颜色和样式（保持原样）
    colors = {
        'ye_opt算法': '#1f77b4',
        'aggre算法': '#2ca02c',
        'srpt算法': '#d62728',
        'WOA算法': '#9467bd',
        'PipelinedWOA算法': '#ff7f0e',
        'EnhancedPipelinedWOA算法': '#e377c2'
    }
    
    line_widths = {
        'ye_opt算法': 1.5,
        'aggre算法': 1.5,
        'srpt算法': 1.5,
        'WOA算法': 2.5,
        'PipelinedWOA算法': 2.5,
        'EnhancedPipelinedWOA算法': 2.5
    }
    
    markers = {
        'ye_opt算法': 'o',
        'aggre算法': 's',
        'srpt算法': '^',
        'WOA算法': 'D',
        'PipelinedWOA算法': 'v',
        'EnhancedPipelinedWOA算法': '*'
    }
    
    marker_sizes = {
        'ye_opt算法': 7,
        'aggre算法': 7,
        'srpt算法': 7,
        'WOA算法': 8,
        'PipelinedWOA算法': 8,
        'EnhancedPipelinedWOA算法': 8
    }
    
    # 为每个性能指标绘制一个子图
    for idx, (metric, metric_name) in enumerate(metrics.items()):
        ax = axes[idx]
        
        # 获取所有算法在当前指标下的值
        all_values = []
        woa_values = []  # 专门存储WOA系列算法的值
        for algo in algorithms:
            values = performance_data[algo][metric]
            all_values.extend(values)
            if algo in ['WOA算法', 'PipelinedWOA算法', 'EnhancedPipelinedWOA算法']:
                woa_values.extend(values)
        
        # 计算全局范围和WOA系列范围
        min_val = min(all_values)
        max_val = max(all_values)
        woa_min = min(woa_values) if woa_values else min_val
        woa_max = max(woa_values) if woa_values else max_val
        
        # 动态设置y轴范围（关键修改部分）
        if woa_values:
            # 对WOA系列区域进行放大（1.8倍放大系数）
            y_min = max(0, min(min_val, woa_min * 0.95))  # 确保不出现负值
            y_max = max(max_val, woa_max * 1.8)
            
            # 如果放大后范围过大（超过原始范围的3倍），则适当缩小放大系数
            if (y_max - y_min) > 3 * (max_val - min_val):
                y_max = max(max_val, woa_max * 1.3)
        else:
            # 如果没有WOA系列算法，保持原始范围
            y_min = max(0, min_val - (max_val - min_val) * 0.1)
            y_max = max_val + (max_val - min_val) * 0.1
        
        # 绘制所有算法（保持原样）
        for algo in algorithms:
            values = performance_data[algo][metric]
            ax.plot(range(len(erasure_params)), values,
                   label=algo,
                   color=colors[algo],
                   linestyle='-',
                   marker=markers[algo],
                   linewidth=line_widths[algo],
                   markersize=marker_sizes[algo])
        
        # 设置y轴范围和刻度（关键修改部分）
        ax.set_ylim(y_min, y_max)
        
        # 动态调整刻度密度
        if woa_values:
            # 在WOA区域增加更多刻度（15-20个）
            ax.yaxis.set_major_locator(plt.MaxNLocator(15 if len(erasure_params)<=6 else 20))
        else:
            ax.yaxis.set_major_locator(plt.MaxNLocator(10))
        
        ax.set_title(f'{metric_name}对比', fontsize=14, pad=15)
        ax.set_xlabel('纠删码参数(n,k)', fontsize=12)
        ax.set_ylabel(metric_name, fontsize=12)
        ax.set_xticks(range(len(erasure_params)))
        ax.set_xticklabels(erasure_params, rotation=45)
        
        # 添加网格和图例（保持原样）
        ax.grid(True, linestyle='--', alpha=0.3)
        ax.legend(bbox_to_anchor=(0.5, -0.35), loc='upper center', ncol=2)
    
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.25)
    plt.savefig('results/erasure_code_comparison_enhanced.png', dpi=300, bbox_inches='tight')
    plt.close()

"""



def main():
    # 创建results目录（如果不存在）
    if not os.path.exists('results'):
        os.makedirs('results')
    
    print("开始运行实验...")
    start_time = time.time()
    
    try:
        # 运行带宽范围实验
        print("\n运行带宽范围实验...")
        results = run_rs_code_comparison()
        
        # 计算总运行时间
        end_time = time.time()
        total_time = end_time - start_time
        print(f"\n所有实验完成！")
        print(f"总运行时间: {total_time:.2f}秒 ({total_time/60:.2f}分钟)")
        
    except Exception as e:
        print(f"实验运行出错: {str(e)}")
        raise

def save_solutions_to_file(filename, solutions):
    """将解决方案保存到文件中"""
    try:
        # 确保目录存在
        import os
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        with open(filename, 'w') as f:
            # 如果solutions是列表，每个元素一行
            if isinstance(solutions, list):
                for solution in solutions:
                    f.write(str(solution) + '\n')
            else:
                # 如果不是列表，直接写入
                f.write(str(solutions))
                
    except Exception as e:
        print(f"保存解决方案到文件 {filename} 时出错: {e}")

def run_all_algorithms(network_config, n, k):
    """运行所有算法并返回结果和迭代数据"""
    results = {}
    iteration_data = {}    # 运行各个算法
    ye_opt = GAsye.GeneticAlgorithm_ye()  # 遗传算法
    results['ye_opt算法'] = ye_opt.run(n, k, network_config)
      # 聚合树算法
    results['aggre算法'] = AggreTree.run(n, k, network_config)
    
    srpt_algo = SRPT()  # SRPT算法
    results['srpt算法'] = srpt_algo.run(n, k, network_config)
      # 运行WOA系列算法并收集迭代数据
    woa = WhaleOptimizationAlgorithm(nwhales=30, max_iter=100)
    woa_result, woa_iterations = woa.run(n, k, network_config, return_iterations=True)
    results['WOA算法'] = woa_result
    iteration_data['WOA算法'] = {'iterations': list(range(len(woa_iterations))), 'fitness': woa_iterations}
    
    pwoa = PipelinedWOA(nwhales=30, max_iter=100)
    pwoa_result, pwoa_iterations = pwoa.run(n, k, network_config, return_iterations=True)
    results['PipelinedWOA算法'] = pwoa_result
    iteration_data['PipelinedWOA算法'] = {'iterations': list(range(len(pwoa_iterations))), 'fitness': pwoa_iterations}
    
    epwoa = EnhancedPipelinedWOA(nwhales=30, max_iter=100)
    epwoa_result, epwoa_iterations = epwoa.run(n, k, network_config, return_iterations=True)
    results['EnhancedPipelinedWOA算法'] = epwoa_result
    iteration_data['EnhancedPipelinedWOA算法'] = {'iterations': list(range(len(epwoa_iterations))), 'fitness': epwoa_iterations}
    
    return results, iteration_data

if __name__ == "__main__":
    main()

