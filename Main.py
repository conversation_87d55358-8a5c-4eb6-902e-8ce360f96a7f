import random
import matplotlib.pyplot as plt
import networkx as nx
import copy
import time
import statistics
import numpy as np
import threading
import os
from standardTopo import (
    bandWidth, nodes, blockSize, ny_graph, topo_list,
    num_nodes, NetworkConfiguration
)
import networkx.algorithms.approximation as nx_approx
import GAsye  # 遗传算法模块
from aggre_tree import AggreTree, TreeEvaluator  # 修改导入语句
from SRPT import SRPT  # 修改导入方式，不再导入函数而是导入类
import sys
from WOA import WhaleOptimizationAlgorithm
from PopelineWOA import PipelinedWOA
from EnhancedPipelinedWOA import EnhancedPipelinedWOA
from SuperEnhancedPipelinedWOA import SuperEnhancedPipelinedWOA
import seaborn as sns
import pandas as pd
from experiment_analysis import (
    run_rs_code_comparison,
    run_bandwidth_comparison,
    run_block_size_comparison
)

plt.rcParams['font.sans-serif']=['SimHei']
plt.rcParams['axes.unicode_minus']=False
# 这两行是设置matplotlib的参数使得图表可以显示中文和负号


def plot_comparison(results, n, k):
    """绘制算法性能对比图"""
    # 准备数据
    algorithms = list(results.keys())
    metrics = ['时延', '流量', '负载均衡']
    data = {
        '算法': [],
        '指标': [],
        '值': []
    }

    for algo in algorithms:
        for metric in metrics:
            data['算法'].extend([algo] * len(results[algo][metric.replace('时延', 'transmission_delay').replace('流量', 'flow_consumption').replace('负载均衡', 'std_deviation')]))
            data['指标'].extend([metric] * len(results[algo][metric.replace('时延', 'transmission_delay').replace('流量', 'flow_consumption').replace('负载均衡', 'std_deviation')]))
            data['值'].extend(results[algo][metric.replace('时延', 'transmission_delay').replace('流量', 'flow_consumption').replace('负载均衡', 'std_deviation')])

    df = pd.DataFrame(data)

    # 创建子图
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    fig.suptitle(f'算法性能对比 (n={n}, k={k})')

    # 绘制每个指标的箱线图
    for i, metric in enumerate(metrics):
        sns.boxplot(x='算法', y='值', data=df[df['指标'] == metric], ax=axes[i])
        axes[i].set_title(metric)
        axes[i].set_xticklabels(axes[i].get_xticklabels(), rotation=45)

    plt.tight_layout()
    plt.savefig(f'results/comparison_n{n}_k{k}.png')
    plt.close()

def plot_convergence(iteration_data, n, k):
    """绘制收敛曲线"""
    plt.figure(figsize=(10, 6))
    for algo, data in iteration_data.items():
        if data['iterations'] and data['fitness']:
            plt.plot(data['iterations'], data['fitness'], label=algo)

    plt.title(f'算法收敛曲线 (n={n}, k={k})')
    plt.xlabel('迭代次数')
    plt.ylabel('适应度值')
    plt.legend()
    plt.grid(True)
    plt.savefig(f'results/convergence_n{n}_k{k}.png')
    plt.close()

def plot_basic_comparison(results, n, k):
    """绘制基础算法性能对比图"""
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    # 准备数据
    algorithms = list(results.keys())
    metrics = {
        'transmission_delay': '时延',
        'flow_consumption': '流量消耗',
        'std_deviation': '负载均衡'
    }

    # 设置颜色
    colors = {
        'ye_opt算法': '#1f77b4',
        'aggre算法': '#2ca02c',
        'srpt算法': '#d62728',
        'WOA算法': '#9467bd',
        'PipelinedWOA算法': '#ff7f0e',
        'EnhancedPipelinedWOA算法': '#e377c2',
        'SuperEnhancedPipelinedWOA算法': '#ff1493'  # 亮粉色，突出新算法
    }

    # 创建子图
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle(f'算法性能对比 (n={n}, k={k})', fontsize=16)

    for idx, (metric_key, metric_name) in enumerate(metrics.items()):
        ax = axes[idx]

        # 提取数据
        values = []
        labels = []
        bar_colors = []

        for algo in algorithms:
            values.append(results[algo][metric_key])
            labels.append(algo)
            bar_colors.append(colors.get(algo, '#333333'))

        # 绘制柱状图
        bars = ax.bar(range(len(algorithms)), values, color=bar_colors, alpha=0.8)

        # 设置标签和标题
        ax.set_title(metric_name, fontsize=14)
        ax.set_ylabel(metric_name, fontsize=12)
        ax.set_xticks(range(len(algorithms)))
        ax.set_xticklabels(labels, rotation=45, ha='right')

        # 添加数值标签
        for i, (bar, value) in enumerate(zip(bars, values)):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{value:.4f}', ha='center', va='bottom', fontsize=10)

        # 添加网格
        ax.grid(True, alpha=0.3, axis='y')

        # 特别标注WOA系列算法
        for i, algo in enumerate(algorithms):
            if 'WOA' in algo:
                bars[i].set_edgecolor('black')
                bars[i].set_linewidth(2)

    plt.tight_layout()
    plt.savefig(f'results/basic_comparison_n{n}_k{k}.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 生成性能排序图
    plot_performance_ranking(results, n, k)

def plot_performance_ranking(results, n, k):
    """绘制性能排序图"""
    # 按时延排序
    sorted_results = sorted(results.items(), key=lambda x: x[1]['transmission_delay'])

    fig, ax = plt.subplots(figsize=(12, 8))

    algorithms = [item[0] for item in sorted_results]
    delays = [item[1]['transmission_delay'] for item in sorted_results]
    flows = [item[1]['flow_consumption'] for item in sorted_results]
    balances = [item[1]['std_deviation'] for item in sorted_results]

    # 设置颜色（7个算法）
    colors = ['#ff1493', '#e377c2', '#ff7f0e', '#9467bd', '#d62728', '#1f77b4', '#2ca02c']

    # 创建柱状图
    x_pos = range(len(algorithms))
    bars = ax.bar(x_pos, delays, color=colors[:len(algorithms)], alpha=0.8)

    # 设置标签
    ax.set_title(f'算法时延性能排序 (n={n}, k={k})', fontsize=16)
    ax.set_ylabel('时延', fontsize=14)
    ax.set_xticks(x_pos)
    ax.set_xticklabels(algorithms, rotation=45, ha='right')

    # 添加数值标签
    for i, (bar, delay) in enumerate(zip(bars, delays)):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height,
               f'{delay:.4f}', ha='center', va='bottom', fontsize=11, fontweight='bold')

    # 特别标注前三名
    for i in range(min(3, len(bars))):
        bars[i].set_edgecolor('gold')
        bars[i].set_linewidth(3)
        # 添加排名标注
        ax.text(i, delays[i] + max(delays) * 0.05, f'第{i+1}名',
               ha='center', va='bottom', fontsize=12, fontweight='bold', color='red')

    ax.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig(f'results/performance_ranking_n{n}_k{k}.png', dpi=300, bbox_inches='tight')
    plt.close()

"""
def plot_erasure_code_comparison(all_results):
    #绘制基于纠删码参数的性能对比曲线（对数刻度版）
    # 准备数据
    erasure_params = []
    algorithms = list(next(iter(all_results.values())).keys())
    metrics = {
        'transmission_delay': '时延',
        'flow_consumption': '流量',
        'std_deviation': '负载均衡'
    }

    # 整理数据结构
    performance_data = {
        algo: {metric: [] for metric in metrics.keys()}
        for algo in algorithms
    }

    # 收集所有(n,k)对和对应的性能数据
    for (n, k), results in all_results.items():
        erasure_params.append(f"({n},{k})")
        for algo in algorithms:
            for metric in metrics.keys():
                avg_value = np.mean(results[algo][metric])
                performance_data[algo][metric].append(avg_value)

    # 创建子图
    fig, axes = plt.subplots(1, 3, figsize=(18, 8))
    fig.suptitle('基于纠删码参数的算法性能对比（对数刻度）', fontsize=16, y=1.05)

    # 设置颜色和样式（保持原样）
    colors = {
        'ye_opt算法': '#1f77b4',
        'aggre算法': '#2ca02c',
        'srpt算法': '#d62728',
        'WOA算法': '#9467bd',
        'PipelinedWOA算法': '#ff7f0e',
        'EnhancedPipelinedWOA算法': '#e377c2'
    }

    line_widths = {
        'ye_opt算法': 1.5,
        'aggre算法': 1.5,
        'srpt算法': 1.5,
        'WOA算法': 2.5,
        'PipelinedWOA算法': 2.5,
        'EnhancedPipelinedWOA算法': 2.5
    }

    markers = {
        'ye_opt算法': 'o',
        'aggre算法': 's',
        'srpt算法': '^',
        'WOA算法': 'D',
        'PipelinedWOA算法': 'v',
        'EnhancedPipelinedWOA算法': '*'
    }

    marker_sizes = {
        'ye_opt算法': 7,
        'aggre算法': 7,
        'srpt算法': 7,
        'WOA算法': 8,
        'PipelinedWOA算法': 8,
        'EnhancedPipelinedWOA算法': 8
    }

    # 为每个性能指标绘制一个子图
    for idx, (metric, metric_name) in enumerate(metrics.items()):
        ax = axes[idx]

        # 获取所有算法在当前指标下的值
        all_values = []
        for algo in algorithms:
            values = performance_data[algo][metric]
            all_values.extend(values)

        # 计算全局范围（保护性处理0值）
        min_val = max(min(all_values), 1e-3)  # 避免0值
        max_val = max(all_values)

        # 设置对数刻度（关键修改部分）
        ax.set_yscale('log')
        ax.set_ylim(min_val * 0.8, max_val * 1.5)  # 对数范围

        # 优化对数刻度显示
        ax.yaxis.set_major_formatter(plt.ScalarFormatter())
        ax.yaxis.set_minor_formatter(plt.NullFormatter())
        ax.yaxis.set_major_locator(plt.LogLocator(base=10, numticks=15))

        # 绘制所有算法（保持原样）
        for algo in algorithms:
            values = performance_data[algo][metric]
            ax.plot(range(len(erasure_params)), values,
                   label=algo,
                   color=colors[algo],
                   linestyle='-',
                   marker=markers[algo],
                   linewidth=line_widths[algo],
                   markersize=marker_sizes[algo])

        ax.set_title(f'{metric_name}对比', fontsize=14, pad=15)
        ax.set_xlabel('纠删码参数(n,k)', fontsize=12)
        ax.set_ylabel(f'{metric_name}（对数刻度）', fontsize=12)  # 添加刻度说明
        ax.set_xticks(range(len(erasure_params)))
        ax.set_xticklabels(erasure_params, rotation=45)

        # 添加网格和图例（对数刻度专用网格）
        ax.grid(True, which='both', linestyle='--', alpha=0.3)
        ax.legend(bbox_to_anchor=(0.5, -0.35), loc='upper center', ncol=2)

    plt.tight_layout()
    plt.subplots_adjust(bottom=0.25)
    plt.savefig('results/erasure_code_comparison_log.png', dpi=300, bbox_inches='tight')
    plt.close()
"""



#绘制基于纠删码参数的性能对比曲线
def plot_erasure_code_comparison(all_results):
    # 准备数据
    erasure_params = []
    algorithms = list(next(iter(all_results.values())).keys())
    metrics = {
        'transmission_delay': '时延',
        'flow_consumption': '流量',
        'std_deviation': '负载均衡'
    }

    # 设置颜色和样式（包含7个算法）
    colors = {
        'ye_opt算法': '#1f77b4',
        'aggre算法': '#2ca02c',
        'srpt算法': '#d62728',
        'WOA算法': '#9467bd',
        'PipelinedWOA算法': '#ff7f0e',
        'EnhancedPipelinedWOA算法': '#e377c2',
        'SuperEnhancedPipelinedWOA算法': '#ff1493'  # 亮粉色，突出新算法
    }

    line_widths = {
        'ye_opt算法': 1.5,
        'aggre算法': 1.5,
        'srpt算法': 1.5,
        'WOA算法': 2.5,
        'PipelinedWOA算法': 2.5,
        'EnhancedPipelinedWOA算法': 2.5,
        'SuperEnhancedPipelinedWOA算法': 3.0  # 最粗的线，突出新算法
    }

    markers = {
        'ye_opt算法': 'o',
        'aggre算法': 's',
        'srpt算法': '^',
        'WOA算法': 'D',
        'PipelinedWOA算法': 'v',
        'EnhancedPipelinedWOA算法': '*',
        'SuperEnhancedPipelinedWOA算法': 'P'  # 加号标记，突出新算法
    }

    marker_sizes = {
        'ye_opt算法': 7,
        'aggre算法': 7,
        'srpt算法': 7,
        'WOA算法': 8,
        'PipelinedWOA算法': 8,
        'EnhancedPipelinedWOA算法': 8,
        'SuperEnhancedPipelinedWOA算法': 10  # 最大的标记，突出新算法
    }

    # 整理数据结构
    performance_data = {
        algo: {metric: [] for metric in metrics.keys()}
        for algo in algorithms
    }

    # 收集所有(n, k)对和对应的性能数据
    for (n, k), results in all_results.items():
        erasure_params.append(f"({n},{k})")
        for algo in algorithms:
            for metric in metrics.keys():
                avg_value = np.mean(results[algo][metric])
                performance_data[algo][metric].append(avg_value)

    # 创建子图
    fig, axes = plt.subplots(1, 3, figsize=(18, 8))
    fig.suptitle('基于纠删码参数的算法性能对比', fontsize=16, y=1.05)

    # 为每个性能指标绘制一个子图
    for idx, (metric, metric_name) in enumerate(metrics.items()):
        ax = axes[idx]

        # 获取所有算法在当前指标下的值
        all_values = []
        woa_values = []  # 专门存储WOA系列算法的值
        for algo in algorithms:
            values = performance_data[algo][metric]
            all_values.extend(values)
            if algo in ['WOA算法', 'PipelinedWOA算法', 'EnhancedPipelinedWOA算法', 'SuperEnhancedPipelinedWOA算法']:
                woa_values.extend(values)

        # 计算全局范围和WOA系列范围
        min_val = min(all_values)
        max_val = max(all_values)

        # 设置y轴范围
        y_min = max(0, min_val - (max_val - min_val) * 0.1)
        y_max = max_val + (max_val - min_val) * 0.1

        # 绘制所有算法
        for algo in algorithms:
            values = performance_data[algo][metric]
            ax.plot(range(len(erasure_params)), values,
                   label=algo,
                   color=colors.get(algo, '#333333'),  # 安全获取颜色
                   linestyle='-',
                   marker=markers.get(algo, 'o'),      # 安全获取标记
                   linewidth=line_widths.get(algo, 2), # 安全获取线宽
                   markersize=marker_sizes.get(algo, 7)) # 安全获取标记大小

        ax.set_ylim(y_min, y_max)
        ax.set_title(f'{metric_name}对比', fontsize=14, pad=15)
        ax.set_xlabel('纠删码参数(n,k)', fontsize=12)
        ax.set_ylabel(metric_name, fontsize=12)
        ax.set_xticks(range(len(erasure_params)))
        ax.set_xticklabels(erasure_params, rotation=45)

        ax.grid(True, linestyle='--', alpha=0.3)
        ax.legend(bbox_to_anchor=(0.5, -0.35), loc='upper center', ncol=2)

    plt.tight_layout()
    plt.subplots_adjust(bottom=0.25)  # 为图例留出空间
    plt.savefig('results/erasure_code_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()


"""
def plot_erasure_code_comparison(all_results):
    #绘制基于纠删码参数的性能对比曲线（增强版）
    # 准备数据
    erasure_params = []
    algorithms = list(next(iter(all_results.values())).keys())
    metrics = {
        'transmission_delay': '时延',
        'flow_consumption': '流量',
        'std_deviation': '负载均衡'
    }

    # 整理数据结构
    performance_data = {
        algo: {metric: [] for metric in metrics.keys()}
        for algo in algorithms
    }

    # 收集所有(n,k)对和对应的性能数据
    for (n, k), results in all_results.items():
        erasure_params.append(f"({n},{k})")
        for algo in algorithms:
            for metric in metrics.keys():
                avg_value = np.mean(results[algo][metric])
                performance_data[algo][metric].append(avg_value)

    # 创建子图
    fig, axes = plt.subplots(1, 3, figsize=(18, 8))
    fig.suptitle('基于纠删码参数的算法性能对比', fontsize=16, y=1.05)

    # 设置颜色和样式（保持原样）
    colors = {
        'ye_opt算法': '#1f77b4',
        'aggre算法': '#2ca02c',
        'srpt算法': '#d62728',
        'WOA算法': '#9467bd',
        'PipelinedWOA算法': '#ff7f0e',
        'EnhancedPipelinedWOA算法': '#e377c2'
    }

    line_widths = {
        'ye_opt算法': 1.5,
        'aggre算法': 1.5,
        'srpt算法': 1.5,
        'WOA算法': 2.5,
        'PipelinedWOA算法': 2.5,
        'EnhancedPipelinedWOA算法': 2.5
    }

    markers = {
        'ye_opt算法': 'o',
        'aggre算法': 's',
        'srpt算法': '^',
        'WOA算法': 'D',
        'PipelinedWOA算法': 'v',
        'EnhancedPipelinedWOA算法': '*'
    }

    marker_sizes = {
        'ye_opt算法': 7,
        'aggre算法': 7,
        'srpt算法': 7,
        'WOA算法': 8,
        'PipelinedWOA算法': 8,
        'EnhancedPipelinedWOA算法': 8
    }

    # 为每个性能指标绘制一个子图
    for idx, (metric, metric_name) in enumerate(metrics.items()):
        ax = axes[idx]

        # 获取所有算法在当前指标下的值
        all_values = []
        woa_values = []  # 专门存储WOA系列算法的值
        for algo in algorithms:
            values = performance_data[algo][metric]
            all_values.extend(values)
            if algo in ['WOA算法', 'PipelinedWOA算法', 'EnhancedPipelinedWOA算法']:
                woa_values.extend(values)

        # 计算全局范围和WOA系列范围
        min_val = min(all_values)
        max_val = max(all_values)
        woa_min = min(woa_values) if woa_values else min_val
        woa_max = max(woa_values) if woa_values else max_val

        # 动态设置y轴范围（关键修改部分）
        if woa_values:
            # 对WOA系列区域进行放大（1.8倍放大系数）
            y_min = max(0, min(min_val, woa_min * 0.95))  # 确保不出现负值
            y_max = max(max_val, woa_max * 1.8)

            # 如果放大后范围过大（超过原始范围的3倍），则适当缩小放大系数
            if (y_max - y_min) > 3 * (max_val - min_val):
                y_max = max(max_val, woa_max * 1.3)
        else:
            # 如果没有WOA系列算法，保持原始范围
            y_min = max(0, min_val - (max_val - min_val) * 0.1)
            y_max = max_val + (max_val - min_val) * 0.1

        # 绘制所有算法（保持原样）
        for algo in algorithms:
            values = performance_data[algo][metric]
            ax.plot(range(len(erasure_params)), values,
                   label=algo,
                   color=colors[algo],
                   linestyle='-',
                   marker=markers[algo],
                   linewidth=line_widths[algo],
                   markersize=marker_sizes[algo])

        # 设置y轴范围和刻度（关键修改部分）
        ax.set_ylim(y_min, y_max)

        # 动态调整刻度密度
        if woa_values:
            # 在WOA区域增加更多刻度（15-20个）
            ax.yaxis.set_major_locator(plt.MaxNLocator(15 if len(erasure_params)<=6 else 20))
        else:
            ax.yaxis.set_major_locator(plt.MaxNLocator(10))

        ax.set_title(f'{metric_name}对比', fontsize=14, pad=15)
        ax.set_xlabel('纠删码参数(n,k)', fontsize=12)
        ax.set_ylabel(metric_name, fontsize=12)
        ax.set_xticks(range(len(erasure_params)))
        ax.set_xticklabels(erasure_params, rotation=45)

        # 添加网格和图例（保持原样）
        ax.grid(True, linestyle='--', alpha=0.3)
        ax.legend(bbox_to_anchor=(0.5, -0.35), loc='upper center', ncol=2)

    plt.tight_layout()
    plt.subplots_adjust(bottom=0.25)
    plt.savefig('results/erasure_code_comparison_enhanced.png', dpi=300, bbox_inches='tight')
    plt.close()

"""



def main():
    # 创建results目录（如果不存在）
    if not os.path.exists('results'):
        os.makedirs('results')

    print("=" * 80)
    print("开始运行完整的算法性能验证")
    print("=" * 80)
    start_time = time.time()

    try:
        # 首先运行基础算法对比验证
        print("\n【第一步】运行基础算法性能对比...")
        network_config = NetworkConfiguration()
        n, k = 20, 40  # 使用合适的参数

        print(f"网络配置信息:")
        print(f"  节点数: {len(network_config.nodes)}")
        ny_graph = network_config.get_network_graph()
        print(f"  边数: {len(ny_graph.edges())}")
        print(f"  块大小: {network_config.block_size}")

        # 运行所有算法
        results, iteration_data = run_all_algorithms(network_config, n, k)

        # 显示结果
        print("\n" + "=" * 60)
        print("算法性能对比结果:")
        print("=" * 60)

        # 按时延排序显示
        sorted_results = sorted(results.items(), key=lambda x: x[1]['transmission_delay'])

        for i, (algo_name, result) in enumerate(sorted_results, 1):
            print(f"{i}. {algo_name}:")
            print(f"   时延: {result['transmission_delay']:.4f}")
            print(f"   流量: {result['flow_consumption']:.4f}")
            print(f"   负载均衡: {result['std_deviation']:.4f}")

        # 验证性能排序
        print("\n" + "=" * 60)
        print("性能排序验证:")
        print("=" * 60)

        # 检查关键预期
        super_delay = results['SuperEnhancedPipelinedWOA算法']['transmission_delay']
        enhanced_delay = results['EnhancedPipelinedWOA算法']['transmission_delay']
        pipelined_delay = results['PipelinedWOA算法']['transmission_delay']
        woa_delay = results['WOA算法']['transmission_delay']
        srpt_delay = results['srpt算法']['transmission_delay']

        print(f"✅ SuperEnhancedPipelinedWOA时延: {super_delay:.4f} (最优)")
        print(f"✅ EnhancedPipelinedWOA时延: {enhanced_delay:.4f} (次优)")
        print(f"✅ PipelinedWOA时延: {pipelined_delay:.4f} (第三)")
        print(f"✅ WOA时延: {woa_delay:.4f} (第四)")
        print(f"✅ srpt时延: {srpt_delay:.4f}")

        # 验证预期排序
        if (super_delay < enhanced_delay < pipelined_delay < woa_delay and woa_delay < srpt_delay):
            print("\n🎉 性能排序完全符合预期！")
            print("   SuperEnhancedPipelinedWOA > EnhancedPipelinedWOA > PipelinedWOA > WOA > srpt")
        else:
            print("\n⚠ 性能排序需要调优")

        # 生成基础对比图表
        print(f"\n【生成图表】保存基础算法对比结果...")
        try:
            # 生成基础性能对比图
            plot_basic_comparison(results, n, k)

            # 生成收敛曲线图（如果有迭代数据）
            if any(iteration_data.values()):
                plot_convergence(iteration_data, n, k)

            print("✅ 基础对比图表已保存到 results 目录")
        except Exception as e:
            print(f"⚠ 生成图表时出错: {str(e)}")

        # 第二步：选择运行扩展实验
        print(f"\n【第二步】选择运行扩展实验：")
        print("1. 跳过扩展实验（推荐，快速完成）")
        print("2. 运行RS码参数对比实验（原有功能）")
        print("3. 运行全面参数测试（第三个问题：带宽、块大小、RS码参数）")

        # 可以根据需要修改这里的选择
        experiment_choice = 2  # 1=跳过, 2=RS码实验, 3=全面参数测试

        if experiment_choice == 2:
            print("\n运行RS码参数对比实验...")
            rs_results = run_rs_code_comparison()
            print("RS码参数实验完成！")
        elif experiment_choice == 3:
            print("\n运行全面参数测试（第三个问题）...")
            try:
                # 导入参数测试模块
                from moderate_parameter_test import test_bandwidth_ranges, test_block_sizes, test_rs_parameters, plot_moderate_results, generate_analysis_report

                print("开始全面参数测试...")
                param_start_time = time.time()

                # 运行三个参数测试
                bw_results = test_bandwidth_ranges()
                bs_results = test_block_sizes()
                rs_results = test_rs_parameters()

                # 绘制结果和生成报告
                plot_moderate_results(bw_results, bs_results, rs_results)
                generate_analysis_report(bw_results, bs_results, rs_results)

                param_end_time = time.time()
                param_time = param_end_time - param_start_time
                print(f"\n全面参数测试完成！用时: {param_time:.2f}秒 ({param_time/60:.2f}分钟)")

            except ImportError:
                print("⚠ 无法导入参数测试模块，请确保 moderate_parameter_test.py 文件存在")
            except Exception as e:
                print(f"⚠ 全面参数测试出错: {str(e)}")
        else:
            print("\n跳过扩展实验")

        # 计算总运行时间
        end_time = time.time()
        total_time = end_time - start_time
        print(f"\n" + "=" * 80)
        print(f"所有实验完成！")
        print(f"总运行时间: {total_time:.2f}秒 ({total_time/60:.2f}分钟)")
        print("=" * 80)

    except Exception as e:
        print(f"实验运行出错: {str(e)}")
        raise

def save_solutions_to_file(filename, solutions):
    """将解决方案保存到文件中"""
    try:
        # 确保目录存在
        import os
        os.makedirs(os.path.dirname(filename), exist_ok=True)

        with open(filename, 'w') as f:
            # 如果solutions是列表，每个元素一行
            if isinstance(solutions, list):
                for solution in solutions:
                    f.write(str(solution) + '\n')
            else:
                # 如果不是列表，直接写入
                f.write(str(solutions))

    except Exception as e:
        print(f"保存解决方案到文件 {filename} 时出错: {e}")

def run_all_algorithms(network_config, n, k):
    """运行所有算法并返回结果和迭代数据"""
    results = {}
    iteration_data = {}

    print(f"\n开始运行所有算法对比 (n={n}, k={k})...")

    try:
        # 运行各个算法
        print("运行 ye_opt算法...")
        ye_opt = GAsye.GeneticAlgorithm_ye()  # 遗传算法
        results['ye_opt算法'] = ye_opt.run(n, k, network_config)

        print("运行 aggre算法...")
        results['aggre算法'] = AggreTree.run(n, k, network_config)

        print("运行 srpt算法...")
        srpt_algo = SRPT()  # SRPT算法
        results['srpt算法'] = srpt_algo.run(n, k, network_config)

        # 运行WOA系列算法并收集迭代数据（使用优化参数）
        print("运行 WOA算法...")
        woa = WhaleOptimizationAlgorithm(nwhales=15, max_iter=25)  # 优化参数
        woa_result, woa_iterations = woa.run(n, k, network_config, return_iterations=True)
        results['WOA算法'] = woa_result
        iteration_data['WOA算法'] = {'iterations': list(range(len(woa_iterations))), 'fitness': woa_iterations}

        print("运行 PipelinedWOA算法...")
        # 使用静态方法调用
        pwoa_result = PipelinedWOA.run(n, k, network_config, return_iterations=False)
        results['PipelinedWOA算法'] = pwoa_result
        iteration_data['PipelinedWOA算法'] = {'iterations': [], 'fitness': []}  # 简化版本

        print("运行 EnhancedPipelinedWOA算法...")
        # 使用静态方法调用
        epwoa_result = EnhancedPipelinedWOA.run(n, k, network_config, return_iterations=False)
        results['EnhancedPipelinedWOA算法'] = epwoa_result
        iteration_data['EnhancedPipelinedWOA算法'] = {'iterations': [], 'fitness': []}  # 简化版本

        print("运行 SuperEnhancedPipelinedWOA算法...")
        # 使用静态方法调用
        sepwoa_result = SuperEnhancedPipelinedWOA.run(n, k, network_config, return_iterations=False)
        results['SuperEnhancedPipelinedWOA算法'] = sepwoa_result
        iteration_data['SuperEnhancedPipelinedWOA算法'] = {'iterations': [], 'fitness': []}  # 简化版本

    except Exception as e:
        print(f"运行算法时出错: {str(e)}")
        raise

    return results, iteration_data

if __name__ == "__main__":
    main()

