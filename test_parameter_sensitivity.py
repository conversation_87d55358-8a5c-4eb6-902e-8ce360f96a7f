#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试参数敏感性 - 验证修复后的算法是否正确响应参数变化
"""

import time
import sys
import threading
import matplotlib.pyplot as plt
import numpy as np
from standardTopo import NetworkConfiguration
from WOA import WhaleOptimizationAlgorithm
from PopelineWOA import PipelinedWOA
from EnhancedPipelinedWOA import EnhancedPipelinedWOA
from SRPT import SRPT

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def run_with_timeout(func, timeout_seconds=60):
    """带超时机制运行函数"""
    result = [None]
    exception = [None]
    
    def target():
        try:
            result[0] = func()
        except Exception as e:
            exception[0] = e
    
    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout_seconds)
    
    if thread.is_alive():
        return None
    elif exception[0] is not None:
        return None
    else:
        return result[0]

def run_single_algorithm(algo_name, n, k, network_config):
    """运行单个算法"""
    def run_algo():
        if algo_name == 'WOA算法':
            woa = WhaleOptimizationAlgorithm(nwhales=8, max_iter=10)
            return woa.run(n, k, network_config)
        elif algo_name == 'PipelinedWOA算法':
            return PipelinedWOA.run(n, k, network_config)
        elif algo_name == 'EnhancedPipelinedWOA算法':
            return EnhancedPipelinedWOA.run(n, k, network_config)
        elif algo_name == 'srpt算法':
            return SRPT.run(n, k, network_config)
        else:
            raise ValueError(f"未知算法: {algo_name}")
    
    timeout_map = {
        'WOA算法': 30,
        'PipelinedWOA算法': 25,
        'EnhancedPipelinedWOA算法': 20,
        'srpt算法': 15
    }
    
    timeout = timeout_map.get(algo_name, 30)
    result = run_with_timeout(run_algo, timeout_seconds=timeout)
    
    if result is not None:
        return result
    else:
        return {
            'transmission_delay': float('inf'),
            'flow_consumption': float('inf'),
            'std_deviation': float('inf')
        }

def test_bandwidth_sensitivity():
    """测试带宽敏感性"""
    print("\n" + "=" * 60)
    print("测试带宽敏感性")
    print("=" * 60)
    
    # 测试不同带宽范围
    bandwidth_ranges = [
        (30, 300),   # 低带宽
        (100, 300),  # 中低带宽
        (200, 300),  # 中高带宽
        (300, 300)   # 高带宽
    ]
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 'srpt算法']
    n, k = 10, 5
    
    results = {}
    for algo in algorithms:
        results[algo] = []
    
    for min_bw, max_bw in bandwidth_ranges:
        print(f"\n测试带宽范围: {min_bw}-{max_bw} Mbps")
        
        network_config = NetworkConfiguration(bandwidth_range=(min_bw, max_bw))
        
        for algo_name in algorithms:
            print(f"  {algo_name}...", end=" ")
            
            result = run_single_algorithm(algo_name, n, k, network_config)
            delay = result['transmission_delay']
            results[algo_name].append(delay)
            
            if delay != float('inf'):
                print(f"时延: {delay:.4f}")
            else:
                print("失败")
    
    return results, bandwidth_ranges

def test_block_size_sensitivity():
    """测试块大小敏感性"""
    print("\n" + "=" * 60)
    print("测试块大小敏感性")
    print("=" * 60)
    
    # 测试不同块大小
    block_sizes = [2, 6, 10, 16]
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 'srpt算法']
    n, k = 10, 5
    
    results = {}
    for algo in algorithms:
        results[algo] = []
    
    for block_size in block_sizes:
        print(f"\n测试块大小: {block_size} MB")
        
        network_config = NetworkConfiguration(block_size=block_size)
        
        for algo_name in algorithms:
            print(f"  {algo_name}...", end=" ")
            
            result = run_single_algorithm(algo_name, n, k, network_config)
            delay = result['transmission_delay']
            results[algo_name].append(delay)
            
            if delay != float('inf'):
                print(f"时延: {delay:.4f}")
            else:
                print("失败")
    
    return results, block_sizes

def test_rs_code_sensitivity():
    """测试RS码参数敏感性"""
    print("\n" + "=" * 60)
    print("测试RS码参数敏感性")
    print("=" * 60)
    
    # 测试不同RS码参数
    rs_params = [(4, 2), (8, 4), (12, 6), (16, 8)]
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 'srpt算法']
    
    results = {}
    for algo in algorithms:
        results[algo] = []
    
    network_config = NetworkConfiguration()
    
    for n, k in rs_params:
        print(f"\n测试RS码参数: ({n}, {k})")
        
        for algo_name in algorithms:
            print(f"  {algo_name}...", end=" ")
            
            result = run_single_algorithm(algo_name, n, k, network_config)
            delay = result['transmission_delay']
            results[algo_name].append(delay)
            
            if delay != float('inf'):
                print(f"时延: {delay:.4f}")
            else:
                print("失败")
    
    return results, rs_params

def plot_sensitivity_results(bw_results, bw_ranges, bs_results, bs_sizes, rs_results, rs_params):
    """绘制敏感性测试结果"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('参数敏感性测试结果', fontsize=16)
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 'srpt算法']
    colors = ['#e377c2', '#ff7f0e', '#9467bd', '#d62728']
    
    # 1. 带宽敏感性
    ax1 = axes[0]
    x_labels = [f"{min_bw}-{max_bw}" for min_bw, max_bw in bw_ranges]
    
    for i, algo in enumerate(algorithms):
        delays = bw_results[algo]
        filtered_delays = [d if d != float('inf') else np.nan for d in delays]
        ax1.plot(range(len(x_labels)), filtered_delays, 'o-', label=algo, color=colors[i], linewidth=2, markersize=6)
    
    ax1.set_title('带宽范围敏感性', fontsize=14)
    ax1.set_xlabel('带宽范围 (Mbps)')
    ax1.set_ylabel('时延')
    ax1.set_xticks(range(len(x_labels)))
    ax1.set_xticklabels(x_labels, rotation=45)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 块大小敏感性
    ax2 = axes[1]
    
    for i, algo in enumerate(algorithms):
        delays = bs_results[algo]
        filtered_delays = [d if d != float('inf') else np.nan for d in delays]
        ax2.plot(bs_sizes, filtered_delays, 'o-', label=algo, color=colors[i], linewidth=2, markersize=6)
    
    ax2.set_title('块大小敏感性', fontsize=14)
    ax2.set_xlabel('块大小 (MB)')
    ax2.set_ylabel('时延')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. RS码参数敏感性
    ax3 = axes[2]
    x_labels = [f"({n},{k})" for n, k in rs_params]
    
    for i, algo in enumerate(algorithms):
        delays = rs_results[algo]
        filtered_delays = [d if d != float('inf') else np.nan for d in delays]
        ax3.plot(range(len(x_labels)), filtered_delays, 'o-', label=algo, color=colors[i], linewidth=2, markersize=6)
    
    ax3.set_title('RS码参数敏感性', fontsize=14)
    ax3.set_xlabel('RS码参数 (n,k)')
    ax3.set_ylabel('时延')
    ax3.set_xticks(range(len(x_labels)))
    ax3.set_xticklabels(x_labels, rotation=45)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('results/parameter_sensitivity_test.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 参数敏感性测试图已保存: results/parameter_sensitivity_test.png")

def analyze_sensitivity(results, param_name, param_values):
    """分析参数敏感性"""
    print(f"\n【{param_name}敏感性分析】:")
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 'srpt算法']
    
    for algo in algorithms:
        delays = [d for d in results[algo] if d != float('inf')]
        if len(delays) >= 2:
            min_delay = min(delays)
            max_delay = max(delays)
            variation = (max_delay - min_delay) / min_delay * 100
            print(f"  {algo}: 变化范围 {min_delay:.4f} ~ {max_delay:.4f} (变化幅度: {variation:.1f}%)")
        else:
            print(f"  {algo}: 数据不足或运行失败")

def main():
    """主函数"""
    print("=" * 80)
    print("参数敏感性测试 - 验证修复后的算法是否正确响应参数变化")
    print("=" * 80)
    
    total_start_time = time.time()
    
    try:
        # 测试带宽敏感性
        bw_results, bw_ranges = test_bandwidth_sensitivity()
        
        # 测试块大小敏感性
        bs_results, bs_sizes = test_block_size_sensitivity()
        
        # 测试RS码参数敏感性
        rs_results, rs_params = test_rs_code_sensitivity()
        
        # 绘制结果
        plot_sensitivity_results(bw_results, bw_ranges, bs_results, bs_sizes, rs_results, rs_params)
        
        # 分析敏感性
        analyze_sensitivity(bw_results, "带宽范围", bw_ranges)
        analyze_sensitivity(bs_results, "块大小", bs_sizes)
        analyze_sensitivity(rs_results, "RS码参数", rs_params)
        
        # 计算总运行时间
        total_end_time = time.time()
        total_time = total_end_time - total_start_time
        
        print(f"\n" + "=" * 80)
        print("参数敏感性测试完成！")
        print(f"总运行时间: {total_time:.2f}秒 ({total_time/60:.2f}分钟)")
        print("=" * 80)
        
        return bw_results, bs_results, rs_results
        
    except Exception as e:
        print(f"测试运行出错: {str(e)}")
        raise

if __name__ == "__main__":
    try:
        bw_results, bs_results, rs_results = main()
        print("\n🎉 参数敏感性测试成功完成！")
        print("算法现在能够正确响应参数变化！")
    except KeyboardInterrupt:
        print("\n\n用户中断了测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
        sys.exit(1)
