# -*- coding: utf-8 -*-
import time
import random
import ctypes
import collections
import numpy as np
import copy
import math
from deap import base, creator, tools, algorithms
import heapq
from standardTopo import bandWidth, blockSize, ny_graph, topo_list, num_nodes
import networkx as nx
import networkx.algorithms.approximation as nx_approx
import matplotlib.pyplot as pyplot
import statistics

class GeneticAlgorithm_ye:
    def __init__(self):
        random.seed(2)
        self.totalproviders = None 
        self.k = None
        self.targets = None
        self.create_deap_classes()
        self.toolbox = base.Toolbox()
        self.setup_deap_toolbox()

    def create_deap_classes(self):
        # 先清理现有的类定义
        if hasattr(creator, 'FitnessMulti'):
            delattr(creator, 'FitnessMulti')
        if hasattr(creator, 'Individual'):
            delattr(creator, 'Individual')
            
        try:
            # 创建适应度类和个体类
            creator.create("FitnessMulti", base.Fitness, weights=(0.00001, 0.00001, 0.00001, 1.0))
            creator.create("Individual", list, fitness=creator.FitnessMulti, chromosome=None)
        except Exception as e:
            print(f"Error creating DEAP classes in GAsye: {str(e)}")
            # 如果创建失败,重试一次
            if hasattr(creator, 'FitnessMulti'):
                delattr(creator, 'FitnessMulti')
            if hasattr(creator, 'Individual'):
                delattr(creator, 'Individual')
            creator.create("FitnessMulti", base.Fitness, weights=(0.00001, 0.00001, 0.00001, 1.0))
            creator.create("Individual", list, fitness=creator.FitnessMulti, chromosome=None)

    def calcpsdv(self, G, topo_list):
        weight_list = [100 * (bandWidth[edge[0]][edge[1]] - G[edge[0]][edge[1]]['bw']) / bandWidth[edge[0]][edge[1]] for edge in topo_list]
        return statistics.pstdev(weight_list)

    def create_individual(self):
        individual = creator.Individual()
        individual.chromosome = [[] for _ in range(len(self.targets))]
        for index, target in enumerate(self.targets):
            snodes = [random.choice(list(target))]  # 根节点是当前目标和新节点随机选取
            available_providers = list(set(self.totalproviders[index]) - set(snodes))  # 获取对应目标的可用providers

            onetree_chromosome = [[] for _ in range(self.k)]
            for i in range(self.k):
                selected_provider = random.choice(available_providers)
                available_providers.remove(selected_provider)

                onetree_chromosome[i].append(selected_provider)
                snodes.append(selected_provider)

            T = nx_approx.steiner_tree(ny_graph, snodes, weight='true_weight', method='kou')
            for j in range(self.k):
                temppathk = nx.dijkstra_path(T, onetree_chromosome[j][0], snodes[0])
                if not temppathk:
                    temppathk = random.choice(list(nx.all_shortest_paths(T, onetree_chromosome[j][0], target)))
                onetree_chromosome[j] = temppathk

            individual.chromosome[index] = onetree_chromosome

        individual.fitness.values = self.evaluate(individual)
        return individual

    def evaluate(self, individual):
        """评估个体的适应度值"""
        try:
            if not individual.chromosome or not isinstance(individual.chromosome, list):
                return (float('inf'), float('inf'), float('inf'), float('inf'))

            G_tree = copy.deepcopy(ny_graph)
            min_bandwidth_individual = float('inf')
            
            for r, tree_paths in enumerate(individual.chromosome):
                if not isinstance(tree_paths, list):
                    continue
                    
                for path in tree_paths:
                    if not isinstance(path, list):
                        continue
                        
                    min_bandwidth_path = float('inf')
                    for j in range(len(path) - 1):
                        source = path[j]
                        destination = path[j + 1]
                        try:
                            bw = G_tree[source][destination]['bw']
                            if bw < min_bandwidth_path:
                                min_bandwidth_path = bw
                        except (KeyError, TypeError) as e:
                            print(f"Error accessing bandwidth: {e}")
                            continue
                            
                    if min_bandwidth_path < min_bandwidth_individual:
                        min_bandwidth_individual = min_bandwidth_path

            return (1, 1, 1, min_bandwidth_individual)
            
        except Exception as e:
            print(f"Error in evaluate: {str(e)}")
            return (float('inf'), float('inf'), float('inf'), float('inf'))

    def setup_deap_toolbox(self):
        """配置DEAP工具箱，设置遗传算法操作"""
        try:
            # 注册个体创建方法
            self.toolbox.register("individual", self.create_individual)
            self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)
            
            # 注册遗传操作
            self.toolbox.register("evaluate", self.evaluate)
            self.toolbox.register("mate", self.custom_mate)
            self.toolbox.register("mutate", self.custom_mutate)
            self.toolbox.register("select", tools.selTournament, tournsize=3)
            
        except Exception as e:
            print(f"Error in setup_deap_toolbox: {str(e)}")
            raise

    def custom_mate(self, ind1, ind2):
        """自定义交叉操作,确保染色体结构不被破坏"""
        try:
            if not isinstance(ind1.chromosome, list) or not isinstance(ind2.chromosome, list):
                return ind1, ind2

            if len(ind1.chromosome) == 0 or len(ind2.chromosome) == 0:
                return ind1, ind2

            # 选择交叉位置
            size = min(len(ind1.chromosome), len(ind2.chromosome))
            cxpoint = random.randint(0, size - 1)

            # 交换染色体片段
            ind1.chromosome[cxpoint:], ind2.chromosome[cxpoint:] = \
                ind2.chromosome[cxpoint:], ind1.chromosome[cxpoint:]

            return ind1, ind2

        except Exception as e:
            print(f"Error in custom_mate: {str(e)}")
            return ind1, ind2

    def custom_mutate(self, individual):
        """自定义变异操作,确保变异后的染色体仍然有效"""
        try:
            if not isinstance(individual.chromosome, list):
                return individual,

            # 随机选择一个树进行变异
            if len(individual.chromosome) > 0:
                tree_idx = random.randint(0, len(individual.chromosome) - 1)
                if self.totalproviders and self.totalproviders[tree_idx]:
                    # 随机选择新的provider
                    new_provider = random.choice(list(self.totalproviders[tree_idx]))
                    # 尝试创建新的路径
                    if self.targets and self.targets[tree_idx]:
                        target = random.choice(list(self.targets[tree_idx]))
                        try:
                            new_path = nx.shortest_path(ny_graph, new_provider, target)
                            if new_path and len(new_path) >= 2:
                                if individual.chromosome[tree_idx]:
                                    individual.chromosome[tree_idx][0] = new_path
                        except nx.NetworkXNoPath:
                            pass

            return individual,

        except Exception as e:
            print(f"Error in custom_mutate: {str(e)}")
            return individual,

    def run(self, n, k, network_config):
        """运行遗传算法并返回结果"""
        try:
            self.k = k
            self.totalproviders = network_config.totalproviders
            self.targets = network_config.targets
            
            # 创建初始种群
            pop = self.toolbox.population(n=50)  # 种群大小设为50
            
            # 记录最优解
            best_individual = None
            best_fitness = (float('inf'), float('inf'), float('inf'), float('inf'))
            
            # 进化100代
            for gen in range(100):
                # 选择下一代个体
                offspring = self.toolbox.select(pop, len(pop))
                offspring = list(map(copy.deepcopy, offspring))
                
                # 对选中的个体进行交叉和变异
                for child1, child2 in zip(offspring[::2], offspring[1::2]):
                    if random.random() < 0.7:  # 交叉概率
                        self.toolbox.mate(child1, child2)
                
                for mutant in offspring:
                    if random.random() < 0.2:  # 变异概率
                        self.toolbox.mutate(mutant)
                
                # 评估新个体的适应度
                for ind in offspring:
                    if not ind.fitness.valid:
                        ind.fitness.values = self.evaluate(ind)
                        if ind.fitness.values[3] < best_fitness[3]:
                            best_fitness = ind.fitness.values
                            best_individual = copy.deepcopy(ind)
                
                # 选择新一代群体
                pop[:] = offspring
                
            # 返回最优结果
            if best_individual is None:
                return {
                    'transmission_delay': float('inf'),
                    'flow_consumption': float('inf'),
                    'std_deviation': float('inf')
                }
                
            return {
                'transmission_delay': best_fitness[0],
                'flow_consumption': best_fitness[1],
                'std_deviation': best_fitness[2]
            }
            
        except Exception as e:
            print(f"Error in GAsye.run: {str(e)}")
            return {
                'transmission_delay': float('inf'),
                'flow_consumption': float('inf'),
                'std_deviation': float('inf')
            }