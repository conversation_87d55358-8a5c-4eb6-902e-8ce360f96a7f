#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试RS码参数敏感性 - 验证修复后的效果
"""

import matplotlib.pyplot as plt
from standardTopo import NetworkConfiguration
from parameter_sensitive_algorithms import run_parameter_sensitive_algorithm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def test_rs_sensitivity():
    """专门测试RS码参数敏感性"""
    print("=" * 60)
    print("测试RS码参数敏感性 - 验证修复后的效果")
    print("=" * 60)
    
    # RS码参数
    rs_params = [(4, 2), (6, 3), (8, 4), (10, 5), (12, 6), (14, 7), (16, 8), (18, 9)]
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法', 'srpt算法']
    
    results = {}
    for algo in algorithms:
        results[algo] = []
    
    network_config = NetworkConfiguration()
    
    print("\nRS码参数敏感性测试:")
    for n, k in rs_params:
        print(f"\nRS码参数: ({n}, {k}), rs_factor = {k/n:.3f}")
        
        for algo_name in algorithms:
            result = run_parameter_sensitive_algorithm(algo_name, n, k, network_config)
            delay = result['transmission_delay']
            results[algo_name].append(delay)
            print(f"  {algo_name}: {delay:.4f}")
    
    # 分析变化幅度
    print("\n" + "=" * 60)
    print("RS码参数敏感性分析:")
    print("=" * 60)
    
    for algo in algorithms:
        delays = results[algo]
        min_delay = min(delays)
        max_delay = max(delays)
        variation = (max_delay - min_delay) / min_delay * 100
        
        print(f"\n{algo}:")
        print(f"  时延范围: {min_delay:.4f} ~ {max_delay:.4f}")
        print(f"  变化幅度: {variation:.1f}%")
        print(f"  详细数据: {[f'{d:.4f}' for d in delays]}")
    
    # 绘制图表
    plt.figure(figsize=(12, 8))
    
    colors = ['#e377c2', '#ff7f0e', '#9467bd', '#d62728']
    rs_labels = [f"({n},{k})" for n, k in rs_params]
    
    for i, algo in enumerate(algorithms):
        plt.plot(range(len(rs_labels)), results[algo], 'o-', 
                label=algo, color=colors[i], linewidth=2, markersize=6)
    
    plt.title('RS码参数对算法时延的影响（修复后）', fontsize=14)
    plt.xlabel('RS码参数 (n,k)')
    plt.ylabel('时延')
    plt.xticks(range(len(rs_labels)), rs_labels, rotation=45)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('results/rs_sensitivity_fixed.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"\n✅ RS码敏感性测试图已保存: results/rs_sensitivity_fixed.png")
    
    return results

if __name__ == "__main__":
    results = test_rs_sensitivity()
    print("\n🎉 RS码敏感性测试完成！")
