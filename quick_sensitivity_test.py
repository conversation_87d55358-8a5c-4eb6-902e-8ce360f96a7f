#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速敏感性测试 - 验证EnhancedPipelinedWOA修复效果
"""

from standardTopo import NetworkConfiguration
from parameter_sensitive_algorithms import run_parameter_sensitive_algorithm

def quick_test():
    """快速测试EnhancedPipelinedWOA的敏感性"""
    print("=" * 60)
    print("快速敏感性测试 - 验证EnhancedPipelinedWOA修复效果")
    print("=" * 60)
    
    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法']
    
    # 测试块大小敏感性
    print("\n【块大小敏感性测试】:")
    block_sizes = [2, 6, 10, 14, 16]
    
    for algo in algorithms:
        print(f"\n{algo}:")
        delays = []
        for block_size in block_sizes:
            network_config = NetworkConfiguration(block_size=block_size)
            result = run_parameter_sensitive_algorithm(algo, 14, 7, network_config)
            delay = result['transmission_delay']
            delays.append(delay)
            print(f"  块大小 {block_size}MB: {delay:.4f}")
        
        # 分析变化
        min_delay = min(delays)
        max_delay = max(delays)
        variation = (max_delay - min_delay) / min_delay * 100
        unique_values = len(set([round(d, 4) for d in delays]))
        
        print(f"  变化范围: {min_delay:.4f} ~ {max_delay:.4f}")
        print(f"  变化幅度: {variation:.1f}%")
        print(f"  不同值数量: {unique_values}/{len(delays)}")
        
        if unique_values <= 2:
            print(f"  ❌ 仍有严重直线问题")
        elif variation < 10:
            print(f"  ⚠ 变化幅度较小")
        else:
            print(f"  ✅ 敏感性良好")
    
    # 测试RS码参数敏感性
    print("\n【RS码参数敏感性测试】:")
    rs_params = [(4, 2), (8, 4), (12, 6), (16, 8), (18, 9)]
    
    network_config = NetworkConfiguration()
    
    for algo in algorithms:
        print(f"\n{algo}:")
        delays = []
        for n, k in rs_params:
            result = run_parameter_sensitive_algorithm(algo, n, k, network_config)
            delay = result['transmission_delay']
            delays.append(delay)
            print(f"  RS({n},{k}): {delay:.4f}")
        
        # 分析变化
        min_delay = min(delays)
        max_delay = max(delays)
        variation = (max_delay - min_delay) / min_delay * 100
        unique_values = len(set([round(d, 4) for d in delays]))
        
        print(f"  变化范围: {min_delay:.4f} ~ {max_delay:.4f}")
        print(f"  变化幅度: {variation:.1f}%")
        print(f"  不同值数量: {unique_values}/{len(delays)}")
        
        if unique_values <= 2:
            print(f"  ❌ 仍有严重直线问题")
        elif variation < 10:
            print(f"  ⚠ 变化幅度较小")
        else:
            print(f"  ✅ 敏感性良好")

if __name__ == "__main__":
    quick_test()
    print("\n🎉 快速敏感性测试完成！")
