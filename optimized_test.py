#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化测试脚本 - 验证流水线化算法的性能优势
根据项目设计，应该体现：
1. 基于异或合并修复树模型的4个算法：ye_opt, aggre, srpt, WOA
2. 基于流水线化异或合并修复树模型的2个算法：PipelinedWOA, EnhancedPipelinedWOA
3. 性能预期：EnhancedPipelinedWOA > PipelinedWOA > WOA > 其他算法
"""

import time
import sys
import threading
from standardTopo import NetworkConfiguration
from WOA import WhaleOptimizationAlgorithm
from PopelineWOA import PipelinedWOA
from EnhancedPipelinedWOA import EnhancedPipelinedWOA
import GAsye
from aggre_tree import AggreTree
from SRPT import SRPT

def run_with_timeout(func, timeout_seconds=180):
    """带超时机制运行函数"""
    result = [None]
    exception = [None]
    
    def target():
        try:
            result[0] = func()
        except Exception as e:
            exception[0] = e
    
    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout_seconds)
    
    if thread.is_alive():
        print(f"算法运行超时 ({timeout_seconds}秒)")
        return None
    elif exception[0] is not None:
        print(f"算法运行出错: {str(exception[0])}")
        return None
    else:
        return result[0]

def test_algorithm_with_retry(algo_name, n, k, network_config, max_retries=2):
    """测试单个算法，支持重试机制"""
    print(f"\n开始测试 {algo_name}...")
    print(f"参数: n={n}, k={k}")
    
    for attempt in range(max_retries + 1):
        if attempt > 0:
            print(f"  第{attempt + 1}次尝试...")
        
        start_time = time.time()
        
        def run_algo():
            if algo_name == 'WOA算法':
                print("  调用 WhaleOptimizationAlgorithm...")
                # 使用更小的参数确保能运行成功
                woa = WhaleOptimizationAlgorithm(nwhales=10, max_iter=20)
                return woa.run(n, k, network_config)
            elif algo_name == 'PipelinedWOA算法':
                print("  调用 PipelinedWOA.run...")
                return PipelinedWOA.run(n, k, network_config)
            elif algo_name == 'EnhancedPipelinedWOA算法':
                print("  调用 EnhancedPipelinedWOA.run...")
                return EnhancedPipelinedWOA.run(n, k, network_config)
            elif algo_name == 'ye_opt算法':
                print("  调用 GAsye.run...")
                ga = GAsye.GeneticAlgorithm_ye()
                return ga.run(n, k, network_config)
            elif algo_name == 'aggre算法':
                print("  调用 AggreTree.run...")
                return AggreTree.run(n, k, network_config)
            elif algo_name == 'srpt算法':
                print("  调用 SRPT.run...")
                return SRPT.run(n, k, network_config)
            else:
                raise ValueError(f"未知算法: {algo_name}")
        
        # 根据算法类型设置不同的超时时间
        timeout_map = {
            'WOA算法': 120,
            'PipelinedWOA算法': 90,  # 流水线化应该更快
            'EnhancedPipelinedWOA算法': 60,  # 优化版本应该最快
            'ye_opt算法': 180,
            'aggre算法': 60,
            'srpt算法': 60
        }
        
        timeout = timeout_map.get(algo_name, 120)
        result = run_with_timeout(run_algo, timeout_seconds=timeout)
        
        end_time = time.time()
        runtime = end_time - start_time
        
        if result is not None:
            print(f"  ✓ 算法运行成功")
            print(f"  运行时间: {runtime:.2f}秒")
            print(f"  时延: {result['transmission_delay']:.4f}")
            print(f"  流量: {result['flow_consumption']:.4f}")
            print(f"  负载均衡: {result['std_deviation']:.4f}")
            return {
                'success': True,
                'runtime': runtime,
                'result': result,
                'attempts': attempt + 1
            }
        else:
            print(f"  ✗ 第{attempt + 1}次尝试失败")
            if attempt < max_retries:
                print(f"  等待2秒后重试...")
                time.sleep(2)
    
    print(f"  ✗ 算法运行失败（已尝试{max_retries + 1}次）")
    return {
        'success': False,
        'runtime': runtime,
        'result': None,
        'attempts': max_retries + 1
    }

def optimized_test():
    """优化测试 - 验证流水线化算法的性能优势"""
    print("=" * 80)
    print("优化测试脚本 - 验证流水线化算法的性能优势")
    print("=" * 80)
    
    # 使用更小的参数确保所有算法都能运行
    n, k = 15, 30
    network_config = NetworkConfiguration()
    
    print(f"网络配置信息:")
    print(f"  节点数: {len(network_config.nodes)}")
    ny_graph = network_config.get_network_graph()
    print(f"  边数: {len(ny_graph.edges())}")
    print(f"  块大小: {network_config.block_size}")
    
    # 按照预期性能排序的算法列表
    algorithms = [
        # 基于异或合并修复树模型的算法（预期性能较低）
        'ye_opt算法',
        'aggre算法', 
        'srpt算法',
        'WOA算法',
        # 基于流水线化异或合并修复树模型的算法（预期性能更高）
        'PipelinedWOA算法',
        'EnhancedPipelinedWOA算法'  # 应该是最优的
    ]
    
    results = {}
    total_start_time = time.time()
    
    for algo_name in algorithms:
        try:
            result = test_algorithm_with_retry(algo_name, n, k, network_config)
            results[algo_name] = result
            
            # 每个算法测试完后暂停一下
            print("  等待3秒后继续...")
            time.sleep(3)
            
        except KeyboardInterrupt:
            print(f"\n用户中断了 {algo_name} 的测试")
            break
        except Exception as e:
            print(f"\n测试 {algo_name} 时发生未预期错误: {str(e)}")
            results[algo_name] = {
                'success': False,
                'runtime': 0,
                'result': None,
                'attempts': 1
            }
    
    total_end_time = time.time()
    total_runtime = total_end_time - total_start_time
    
    print("\n" + "=" * 80)
    print("测试结果总结:")
    print("=" * 80)
    
    # 分类显示结果
    print("\n【基于异或合并修复树模型的算法】:")
    traditional_algos = ['ye_opt算法', 'aggre算法', 'srpt算法', 'WOA算法']
    traditional_successful = []
    
    for algo_name in traditional_algos:
        if algo_name in results:
            result = results[algo_name]
            if result['success']:
                print(f"  {algo_name}: ✓ 成功 ({result['runtime']:.2f}秒, {result['attempts']}次尝试)")
                print(f"    - 时延: {result['result']['transmission_delay']:.4f}")
                print(f"    - 流量: {result['result']['flow_consumption']:.4f}")
                print(f"    - 负载均衡: {result['result']['std_deviation']:.4f}")
                traditional_successful.append((algo_name, result))
            else:
                print(f"  {algo_name}: ✗ 失败 ({result['runtime']:.2f}秒, {result['attempts']}次尝试)")
    
    print("\n【基于流水线化异或合并修复树模型的算法】:")
    pipeline_algos = ['PipelinedWOA算法', 'EnhancedPipelinedWOA算法']
    pipeline_successful = []
    
    for algo_name in pipeline_algos:
        if algo_name in results:
            result = results[algo_name]
            if result['success']:
                print(f"  {algo_name}: ✓ 成功 ({result['runtime']:.2f}秒, {result['attempts']}次尝试)")
                print(f"    - 时延: {result['result']['transmission_delay']:.4f}")
                print(f"    - 流量: {result['result']['flow_consumption']:.4f}")
                print(f"    - 负载均衡: {result['result']['std_deviation']:.4f}")
                pipeline_successful.append((algo_name, result))
            else:
                print(f"  {algo_name}: ✗ 失败 ({result['runtime']:.2f}秒, {result['attempts']}次尝试)")
    
    print(f"\n总运行时间: {total_runtime:.2f}秒 ({total_runtime/60:.2f}分钟)")
    
    # 性能分析和验证
    print("\n" + "=" * 80)
    print("性能分析和验证:")
    print("=" * 80)
    
    all_successful = traditional_successful + pipeline_successful
    
    if all_successful:
        print("\n运行时间排序（从快到慢）:")
        sorted_by_time = sorted(all_successful, key=lambda x: x[1]['runtime'])
        for i, (name, result) in enumerate(sorted_by_time, 1):
            print(f"  {i}. {name}: {result['runtime']:.2f}秒")
        
        print("\n时延性能排序（从低到高）:")
        sorted_by_delay = sorted(all_successful, key=lambda x: x[1]['result']['transmission_delay'])
        for i, (name, result) in enumerate(sorted_by_delay, 1):
            delay = result['result']['transmission_delay']
            print(f"  {i}. {name}: {delay:.4f}")
        
        # 验证流水线化算法的优势
        print("\n" + "=" * 80)
        print("流水线化优势验证:")
        print("=" * 80)
        
        if pipeline_successful:
            pipeline_avg_time = sum(r[1]['runtime'] for r in pipeline_successful) / len(pipeline_successful)
            pipeline_avg_delay = sum(r[1]['result']['transmission_delay'] for r in pipeline_successful) / len(pipeline_successful)
            
            if traditional_successful:
                traditional_avg_time = sum(r[1]['runtime'] for r in traditional_successful) / len(traditional_successful)
                traditional_avg_delay = sum(r[1]['result']['transmission_delay'] for r in traditional_successful) / len(traditional_successful)
                
                time_improvement = ((traditional_avg_time - pipeline_avg_time) / traditional_avg_time) * 100
                delay_improvement = ((traditional_avg_delay - pipeline_avg_delay) / traditional_avg_delay) * 100
                
                print(f"流水线化算法平均运行时间: {pipeline_avg_time:.2f}秒")
                print(f"传统算法平均运行时间: {traditional_avg_time:.2f}秒")
                print(f"运行时间改进: {time_improvement:.1f}%")
                
                print(f"\n流水线化算法平均时延: {pipeline_avg_delay:.4f}")
                print(f"传统算法平均时延: {traditional_avg_delay:.4f}")
                print(f"时延改进: {delay_improvement:.1f}%")
                
                if time_improvement > 0:
                    print("\n✓ 流水线化算法在运行时间上表现更优")
                else:
                    print("\n⚠ 流水线化算法在运行时间上未显示优势")
                
                if delay_improvement > 0:
                    print("✓ 流水线化算法在时延性能上表现更优")
                else:
                    print("⚠ 流水线化算法在时延性能上未显示优势")
            else:
                print("传统算法均未成功运行，无法进行对比")
        else:
            print("流水线化算法均未成功运行")
    
    return results

if __name__ == "__main__":
    try:
        results = optimized_test()
        print("\n测试完成！")
    except KeyboardInterrupt:
        print("\n\n用户中断了测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
        sys.exit(1)
