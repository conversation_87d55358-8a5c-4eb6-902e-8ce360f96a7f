#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试PipelinedWOA算法
"""

import time
import sys
import threading
from standardTopo import NetworkConfiguration
from PopelineWOA import PipelinedWOA

def run_with_timeout(func, timeout_seconds=60):
    """带超时机制运行函数"""
    result = [None]
    exception = [None]
    
    def target():
        try:
            result[0] = func()
        except Exception as e:
            exception[0] = e
    
    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout_seconds)
    
    if thread.is_alive():
        print(f"算法运行超时 ({timeout_seconds}秒)")
        return None
    elif exception[0] is not None:
        print(f"算法运行出错: {str(exception[0])}")
        return None
    else:
        return result[0]

def test_pipelined_woa():
    """专门测试PipelinedWOA算法"""
    print("=" * 80)
    print("专门测试PipelinedWOA算法")
    print("=" * 80)
    
    # 使用更小的参数
    n, k = 15, 30
    network_config = NetworkConfiguration()
    
    print(f"网络配置信息:")
    print(f"  节点数: {len(network_config.nodes)}")
    ny_graph = network_config.get_network_graph()
    print(f"  边数: {len(ny_graph.edges())}")
    print(f"  块大小: {network_config.block_size}")
    
    print(f"\n开始测试 PipelinedWOA算法...")
    print(f"参数: n={n}, k={k}")
    
    start_time = time.time()
    
    def run_algo():
        print("  调用 PipelinedWOA.run...")
        return PipelinedWOA.run(n, k, network_config)
    
    # 设置较短的超时时间
    result = run_with_timeout(run_algo, timeout_seconds=45)
    
    end_time = time.time()
    runtime = end_time - start_time
    
    if result is not None:
        print(f"  ✓ 算法运行成功")
        print(f"  运行时间: {runtime:.2f}秒")
        print(f"  时延: {result['transmission_delay']:.4f}")
        print(f"  流量: {result['flow_consumption']:.4f}")
        print(f"  负载均衡: {result['std_deviation']:.4f}")
        return True
    else:
        print(f"  ✗ 算法运行失败")
        print(f"  运行时间: {runtime:.2f}秒")
        return False

if __name__ == "__main__":
    try:
        success = test_pipelined_woa()
        if success:
            print("\n✅ PipelinedWOA算法测试成功！")
        else:
            print("\n❌ PipelinedWOA算法仍需进一步优化")
    except KeyboardInterrupt:
        print("\n\n用户中断了测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
        sys.exit(1)
