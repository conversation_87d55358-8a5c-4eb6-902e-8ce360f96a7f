参数敏感性分析图（上排三张图）：
a) nwhales参数敏感性分析：

当鲸鱼数量(nwhales)在20-60之间变化时，性能出现波动
最佳性能出现在nwhales=30附近，此时结果最小约为0.06
当nwhales>50时，性能趋于稳定
这说明鲸鱼群体大小需要适中，过大或过小都不利于算法性能
b) max_iter参数敏感性分析：

随着最大迭代次数的增加，性能先提升后趋于稳定
在max_iter=80之后，性能基本保持稳定
这表明算法在80次迭代后基本达到收敛
继续增加迭代次数对提升性能帮助不大
c) k参数敏感性分析：

当k值较小时（2-5），性能保持相对稳定
当k=6时，性能显著下降（指标上升到约0.1）
这说明k值不宜过大，否则会影响算法性能
算法性能对比图（下排两张图）：
a) 算法执行时间随网络规模变化：

WOA算法执行时间增长最平缓
PipelinedWOA执行时间随节点数增加而显著增加，在40个节点时达到峰值约175秒
EnhancedPipelinedWOA的执行时间介于两者之间，增长相对温和
当节点数达到50时，所有算法的执行时间都急剧下降，这可能需要进一步调查
b) 算法性能随网络规模变化：

WOA算法的性能随节点数增加而显著下降，最终达到约0.75的指标值
PipelinedWOA和EnhancedPipelinedWOA表现更好，性能曲线相对平稳
EnhancedPipelinedWOA在大多数情况下性能最好，指标值保持在0.1以下
这表明改进版算法在处理大规模网络时具有明显优势
总结：

算法参数选择建议：

nwhales选择30左右较为合适
max_iter设置为80-100比较合理
k值建议保持在5以下
算法比较结论：

在执行效率上：WOA > EnhancedPipelinedWOA > PipelinedWOA
在解决方案质量上：EnhancedPipelinedWOA > PipelinedWOA > WOA
EnhancedPipelinedWOA在性能和效率上取得了较好的平衡