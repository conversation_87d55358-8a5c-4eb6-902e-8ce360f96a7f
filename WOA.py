# 导入必要的库
import numpy as np
import networkx as nx
import random
import copy
import time
from deap import base, creator
import networkx.algorithms.approximation as nx_approx
from scipy.stats import sem
from standardTopo import bandWidth, blockSize, ny_graph, topo_list, num_nodes

class WhaleOptimizationAlgorithm:
    def __init__(self, nwhales=30, max_iter=100, seed=3):
        """初始化鲸鱼优化算法

        Args:
            nwhales (int): 种群规模，即鲸鱼个体数量
            max_iter (int): 最大迭代次数
            seed (int): 随机数种子，用于结果复现
        """
        # 设置随机数种子
        random.seed(seed)
        np.random.seed(seed)
        
        # 基本参数设置
        self.nwhales = nwhales  # 种群规模
        self.max_iter = max_iter  # 最大迭代次数
        self.current_iter = 0  # 当前迭代次数
        
        # 优化相关参数
        self.network_config = None  # 网络配置
        self.best_whale = None  # 最优解
        self.best_fitness = (float('inf'), float('inf'), float('inf'))  # 最优适应度
        self.fitness_history = []  # 迭代历史记录
        self.convergence_data = {  # 详细的收敛数据
            'iterations': [],
            'best_fitness': [],
            'parameters': []  # 记录算法参数
        }
        
        # 问题特定参数
        self.totalproviders = None  # 总提供者
        self.k = None  # k值参数
        self.targets = None  # 目标节点集合
        self.whales = []  # 鲸鱼种群
        
        # 创建DEAP优化框架所需的类
        self.create_deap_classes()

    def create_deap_classes(self):
        """创建DEAP优化框架所需的类"""
        try:
            # 清理之前可能存在的DEAP类
            for name in ['FitnessMin', 'Individual']:
                if hasattr(creator, name):
                    delattr(creator, name)
            
            # 创建适应度类和个体类
            creator.create("FitnessMin", base.Fitness, weights=(-1.0, -1.0, -1.0))  # 三个优化目标
            creator.create("Individual", list, fitness=creator.FitnessMin, wa=None)
        except Exception as e:
            print(f"Error creating DEAP classes: {str(e)}")
            raise

    def run(self, n, k, network_config, return_iterations=False):
        """运行算法并返回结果
        
        Args:
            n: 网络规模参数
            k: 连接数参数
            network_config: 网络配置对象
            return_iterations: 是否返回迭代历史
            
        Returns:
            result: 包含最优解性能指标的字典
            fitness_history: 如果return_iterations为True,返回迭代历史
        """
        self.network_config = network_config
        best_whale, best_fitness = self.optimize(network_config.totalproviders, k, network_config.targets)
        
        # 构造结果字典
        result = {
            'transmission_delay': best_fitness[0],
            'flow_consumption': network_config.block_size * best_fitness[1],
            'std_deviation': best_fitness[2]
        }
        
        if return_iterations:
            return result, self.fitness_history
        return result

    def optimize(self, totalproviders, k, targets):
        """运行优化算法并记录迭代历史"""
        print("鲸鱼算法开始")
        self.totalproviders = totalproviders
        self.k = k
        self.targets = targets
        
        # 清空历史记录
        self.fitness_history = []
        self.convergence_data = {'iterations': [], 'best_fitness': [], 'parameters': []}
        
        # 初始化种群
        self.whales = [self.create_individual() for _ in range(self.nwhales)]
        self.best_whale = copy.deepcopy(self.whales[0].wa)
        self.best_fitness = self.whales[0].fitness.values
        
        # 主循环
        for t in range(self.max_iter):
            # 更新位置
            self.encircling()
            
            # 记录历史
            self.fitness_history.append({
                'transmission_delay': self.best_fitness[0],
                'flow_consumption': self.best_fitness[1],
                'std_deviation': self.best_fitness[2]
            })
            
            # 记录收敛数据
            self.convergence_data['iterations'].append(t)
            self.convergence_data['best_fitness'].append(sum(self.best_fitness))
            self.convergence_data['parameters'].append({
                'nwhales': self.nwhales,
                'current_iter': t
            })
            
            # 每10次迭代输出进度
            if (t + 1) % 10 == 0:
                print(f"迭代 {t + 1}/{self.max_iter}, 当前最优适应度: {sum(self.best_fitness):.4f}")
        
        return self.best_whale, self.best_fitness

    def evaluate(self, individual):
        G_tree = copy.deepcopy(ny_graph)
        for r in range(len(individual.wa)):
            flags = {}
            TreeweightMatrix = [None for i in range(num_nodes)]
            for i in range(len(TreeweightMatrix)):
                TreeweightMatrix[i] = [0 for j in range(num_nodes)]
            for i in range(len(individual.wa[r])):
                path = individual.wa[r][i]
                for l in range(len(path) - 1):
                    source = path[l]
                    destination = path[l + 1]
                    if (r, source, destination) not in flags and (r, destination, source) not in flags:
                        # 更新TreeweightMatrix
                        TreeweightMatrix[source][destination] += 1
                        # 更新路径上的带宽
                        G_tree[source][destination]['bw'] -= blockSize
                        flags[(r, source, destination)], flags[(r, destination, source)] = 1, 1
                    else:
                        continue

            # 计算当前树的时延
            average_maxbefore, num_repeated_nodes = self.sub_evaluate_latency(individual.wa[r], TreeweightMatrix)

        # 计算负载均衡指标
        standard = self.calcpsdv(G_tree, topo_list)
        
        # 计算流量消耗
        total_flow = len(flags)  # 使用的边的数量作为流量指标
        
        # 返回三个指标：时延、流量、负载均衡
        individual.fitness.values = (average_maxbefore, total_flow, standard)
        return individual.fitness.values

    def final_evaluate(self, individual):
        #  print(f"individual[0]:{individual[0]}")
        # print(f"woa individual:{individual}")
        individual = [individual]
        # print(f"new woa individual:{individual}")
        stflow = 0
        G_tree = copy.deepcopy(ny_graph)

        maxLatem = 0

        flags = {}
        for r in range(1):
            '''
            TreeweightMatrix = [None for i in range(num_nodes)]
            for i in range(len(TreeweightMatrix)):
                TreeweightMatrix[i] = [0 for j in range(num_nodes)]
            '''
            TreeweightMatrix = [[0 for _ in range(num_nodes)] for _ in range(num_nodes)]
            for i in range(len(individual[r])):
                path = individual[r][i]
                new_path = True  # 假设路径尚未处理
                for l in range(len(path) - 1):
                    source = path[l]
                    destination = path[l + 1]
                    #  print(f"woa source,destination:{source},{destination}")
                    if (r, source, destination) not in flags and (r, destination, source) not in flags:
                        # 更新TreeweightMatrix
                        TreeweightMatrix[source][destination] += 1
                        # 更新路径上的带宽
                        G_tree[source][destination]['bw'] -= blockSize
                        # 传输数据块
                        stflow += 1
                        flags[(r, source, destination)], flags[(r, destination, source)] = 1, 1
                    else:
                        continue
            latem = self.final_sub_evaluate(individual[r], TreeweightMatrix)  # 每棵树的时延
            maxLatem = max(latem, maxLatem)

        standard = self.calcpsdv(G_tree, topo_list)
        # 确保返回的是普通Python float类型
        values = [float(maxLatem), float(stflow), float(standard)]
        return values

    def final_sub_evaluate(self, onetree_wa, TreeweightMatrix):

        # 统计节点出现次数
        node_count = {}  # 记录节点在所有路径出现次数
        # print("TreeweightMatrix", TreeweightMatrix)
        # 遍历所有路径
        for path in onetree_wa:
            # for node in path[:-1]:
            for index, node in enumerate(path[:-1]):  # 不统计根节点
                # 统计节点出现次数
                if node in node_count:
                    node_count[node] += 1

                else:
                    node_count[node] = 1

        # 找出出现两次以上的节点
        repeated_nodes = [node for node, count in node_count.items() if count >= 2]

        # print("repeated_nodes:",repeated_nodes)
        # 计算从叶子结点到重复节点的最大时延
        node_max_latency = {}  # 记录从叶子结点到重复节点的最大时延
        for path in onetree_wa:
            for node in path:
                node_max_latency[node] = 0

        for path in onetree_wa:
            leaf_node = path[0]
            for index, node in enumerate(path[1:]):
                if node in repeated_nodes:
                    # 计算从叶子结点到节点的时延
                    latency = 0
                    for i in range(index - 1, -1, -1):
                        source = path[i]
                        destination = path[i + 1]
                        latency += blockSize / (bandWidth[source][destination] / TreeweightMatrix[source][destination])

                    node_max_latency[node] = max(node_max_latency[node], latency)
                    # print("node_max_latency[node]", node, node_max_latency[node])

        maxLatem = 0
        for i in range(len(onetree_wa)):
            path = onetree_wa[i]
            latem = 0
            for l in range(len(path) - 1):

                source = path[l]
                destination = path[l + 1]

                # 更新最大传输时延
                if TreeweightMatrix[source][destination] != 0:
                    if source in repeated_nodes:  # 是否重复节点，重复节点就是需要等待并进行合并操作的节点
                        latem = node_max_latency[source] + blockSize / (
                                    bandWidth[source][destination] / TreeweightMatrix[source][destination])
                    else:
                        latem += blockSize / (bandWidth[source][destination] / TreeweightMatrix[source][destination])
                else:
                    print("TreeweightMatrix[source][destination]为0！")

            if latem > maxLatem:
                maxLatem = latem

        return maxLatem

    def sub_evaluate_latency(self, onetree_wa, TreeweightMatrix):

        # 统计节点出现次数
        node_count = {}  # 记录节点出现次数
        node_maxbefore = {}
        # 遍历所有路径
        for path in onetree_wa:
            #for node in path[:-1]:
            for index, node in enumerate(path[:-1]): # 不统计根节点
                # 统计节点出现次数
                if node in node_count:
                    node_count[node] += 1
                    node_maxbefore[node] = max(node_maxbefore[node], index)
                else:
                    node_count[node] = 1
                    node_maxbefore[node] = index

        # 找出出现两次以上的节点
        repeated_nodes = [node for node, count in node_count.items() if count >= 2]

        # 计算这些重复节点前面节点数量的平均值
        total_maxbefore = sum(node_maxbefore[node] for node in repeated_nodes)
        average_maxbefore = total_maxbefore / len(repeated_nodes) if repeated_nodes else 0
        num_repeated_nodes = len(repeated_nodes)

        return  average_maxbefore, num_repeated_nodes


        #  计算标准差
    def calcpsdv(self, G, topo_list):
        # weight_list = [bandWidth[edge[0]][edge[1]] - G[edge[0]][edge[1]]['bw'] for edge in topo_list]
        weight_list = [100 * (bandWidth[edge[0]][edge[1]] - G[edge[0]][edge[1]]['bw']) / bandWidth[edge[0]][edge[1]]
                        for edge in topo_list]
        '''
        # df = pd.DataFrame(weight_list,columns=['weight'])
        # pandas计算总体标准差
        #  overall_std = df['weight'].std(ddof=0)
        # pandas计算样本标准差
        # sample_std = df['weight'].std(ddof=1)
        return sample_std
        '''

        #  sample_std = stats.tstd(weight_list)  # 使用scipy.stats模块计算样本标准差
        #  return sample_std

        '''
        mean = sum(weight_list) / len(weight_list)
        variance = sum((x - mean) ** 2 for x in weight_list) / (len(weight_list) - 1)
        return variance ** 0.5
        '''

        #  return np.std(weight_list, ddof=0)  # 使用numpy计算样本标准差，并将ddof参数设置为1，默认情况下ddof的值为0，用于计算总体标准差
        #  return statistics.pstdev(weight_list)  #使用statistics.stdev计算样本标准差
        '''
        # 以下是tensorflow
        # 将 weight_list 转换为 TensorFlow Tensor
        weights_tf = tf.constant(weight_list, dtype=tf.float32)
        # 计算总体标准差
        overall_std_tf = tf.math.reduce_std(weights_tf)
        return overall_std_tf
        '''
        '''
        # 以下是用pytorch
        # 将 weight_list 转换为 PyTorch Tensor
        weights_torch = torch.tensor(weight_list, dtype=torch.float32)

        # 计算总体标准差
        # overall_std_torch = torch.std(weights_torch, unbiased=False)
        # 计算样本标准差
        sample_std_torch = torch.std(weights_torch, unbiased=True)
        return sample_std_torch
        '''
        '''
        # statistics模块
        overall_std = statistics.stdev(weight_list)  # 样本标准差
        return overall_std
        '''
        '''math模块
        mean = sum(weight_list) / len(weight_list)
        variance = sum((x - mean) ** 2 for x in weight_list) / (len(weight_list) - 1)
        return math.sqrt(variance)
        '''
        '''
        numpy__var函数
        
        weight_array = np.array(weight_list)
        variance = np.var(weight_array, ddof=0)  # 样本方差
        return np.sqrt(variance)
        '''
        '''scipy-sem函数'''
        sample_std = sem(weight_list)  # 样本标准误差
        return sample_std

    #  用于检查是否存在环路
    def has_cycle(self, chromosome):
        """检查是否存在环路"""
        G = nx.DiGraph()

        # 添加路径中的边
        for path in chromosome:
            for i in range(len(path) - 1):
                G.add_edge(path[i], path[i + 1])

        # 使用深度优先搜索检测环路
        try:
            cycle_nodes = nx.find_cycle(G, orientation='original')
            return True, cycle_nodes
        except nx.NetworkXNoCycle:
            return False, []
    
    def create_individual(self):
        try:
            individual = creator.Individual()
            individual.wa = [[] for _ in range(len(self.targets))]
            
            for index, target in enumerate(self.targets):
                if not target:  # 如果target为空
                    continue
                    
                try:
                    target_node = random.choice(list(target))
                except IndexError:
                    print(f"Warning: Empty target set at index {index}")
                    continue
                
                # 确保我们有足够的providers
                if not self.totalproviders[index]:
                    print(f"Warning: No providers available for target {index}")
                    continue
                    
                # 初始化路径列表
                onetree_wa = []
                available_providers = list(self.totalproviders[index][:])  # 创建providers的副本
                
                # 为每个k创建路径
                attempts = 0
                max_attempts = 3  # 最大尝试次数
                
                while len(onetree_wa) < self.k and available_providers and attempts < max_attempts:
                    try:
                        # 选择一个provider
                        provider = random.choice(available_providers)
                        available_providers.remove(provider)  # 从可用列表中移除
                        
                        # 尝试找到从provider到target的路径
                        path = nx.shortest_path(ny_graph, provider, target_node)
                        if path:
                            onetree_wa.append(path)
                            
                    except (nx.NetworkXNoPath, nx.NodeNotFound) as e:
                        print(f"Path finding error: {str(e)}")
                        attempts += 1
                        continue
                        
                if onetree_wa:  # 只有在找到至少一条路径时才添加
                    individual.wa[index] = onetree_wa
            
            # 评估个体适应度
            if not any(individual.wa):  # 如果没有找到任何有效路径
                individual.fitness.values = (float('inf'), float('inf'), float('inf'))
            else:
                individual.fitness.values = self.evaluate(individual)
                # 更新最优解
                if sum(individual.fitness.values) < sum(self.best_fitness):
                    self.best_fitness = individual.fitness.values
                    self.best_whale = copy.deepcopy(individual.wa)
                    
            return individual
            
        except Exception as e:
            print(f"Error in create_individual: {str(e)}")
            # 创建一个默认个体作为备选
            default_individual = creator.Individual()
            default_individual.wa = [[] for _ in range(len(self.targets))]
            default_individual.fitness.values = (float('inf'), float('inf'), float('inf'))
            return default_individual

    def encircling(self):
        """实现鲸鱼优化算法的包围捕食行为"""
        a = 2 * (1 - self.current_iter / self.max_iter)  # 收缩-扩展系数
        b = 1  # 攻击强度系数
        l = np.random.uniform(-1, 1)  # 随机搜索参数
        
        for i in range(self.nwhales):
            A = 2 * a * random.random() - a
            C = 2 * random.random()
            p = random.random()
            h = random.randint(0, 40)
            
            if h < 20:
                p = 0.5  # 随机操作概率
            else:
                p = 1  # 螺旋搜索概率
                
            # 进行搜索操作
            if p < 0.5:
                if abs(A) < 1:
                    self._shrinking_encircling(i, A, C)  # 收缩包围
                else:
                    self._random_search(i)  # 随机搜索
            else:
                self._spiral_search(i, l)  # 螺旋搜索
    
    def _shrinking_encircling(self, whale_index, A, C):
        """收缩包围行为"""
        try:
            if not self.best_whale or not self.whales[whale_index].wa:
                return
                
            for index in range(len(self.whales[whale_index].wa)):
                for j in range(len(self.whales[whale_index].wa[index])):
                    root_node = self.best_whale[index][j][-1]
                    leaf_node = self.whales[whale_index].wa[index][j][0]
                    new_path = self.find_valid_path(leaf_node, root_node)
                    
                    if new_path:
                        self.whales[whale_index].wa[index][j] = new_path
            
            # 评估新解
            self.whales[whale_index].fitness.values = self.evaluate(self.whales[whale_index])
            if sum(self.whales[whale_index].fitness.values) < sum(self.best_fitness):
                self.best_fitness = self.whales[whale_index].fitness.values
                self.best_whale = copy.deepcopy(self.whales[whale_index].wa)
        except Exception as e:
            print(f"Error in shrinking encircling: {str(e)}")
    
    def _random_search(self, whale_index):
        """随机搜索行为"""
        try:
            # 随机选择一个鲸鱼作为目标
            random_whale = self.whales[random.randint(0, self.nwhales - 1)]
            if not random_whale.wa:
                return
                
            for index in range(len(self.whales[whale_index].wa)):
                for j in range(len(self.whales[whale_index].wa[index])):
                    if j >= len(random_whale.wa[index]):
                        continue
                        
                    root_node = random_whale.wa[index][j][-1]
                    leaf_node = self.whales[whale_index].wa[index][j][0]
                    new_path = self.find_valid_path(leaf_node, root_node)
                    
                    if new_path:
                        self.whales[whale_index].wa[index][j] = new_path
            
            # 评估新解
            self.whales[whale_index].fitness.values = self.evaluate(self.whales[whale_index])
            if sum(self.whales[whale_index].fitness.values) < sum(self.best_fitness):
                self.best_fitness = self.whales[whale_index].fitness.values
                self.best_whale = copy.deepcopy(self.whales[whale_index].wa)
        except Exception as e:
            print(f"Error in random search: {str(e)}")
    
    def _spiral_search(self, whale_index, l):
        """螺旋搜索行为"""
        try:
            if not self.best_whale or not self.whales[whale_index].wa:
                return
                
            for index in range(len(self.whales[whale_index].wa)):
                for j in range(len(self.whales[whale_index].wa[index])):
                    root_node = self.best_whale[index][j][-1]
                    leaf_node = self.whales[whale_index].wa[index][j][0]
                    new_path = self.find_valid_path(leaf_node, root_node)
                    
                    if not new_path:
                        new_leaf = self.get_new_leaf_node(leaf_node)
                        new_path = self.find_valid_path(new_leaf, root_node)
                        
                    if new_path:
                        self.whales[whale_index].wa[index][j] = new_path
            
            # 评估新解
            self.whales[whale_index].fitness.values = self.evaluate(self.whales[whale_index])
            if sum(self.whales[whale_index].fitness.values) < sum(self.best_fitness):
                self.best_fitness = self.whales[whale_index].fitness.values
                self.best_whale = copy.deepcopy(self.whales[whale_index].wa)
        except Exception as e:
            print(f"Error in spiral search: {str(e)}")

    def find_valid_path(self, leaf_node, root_node):
        """查找从叶子节点到根节点的有效路径"""
        try:
            paths = list(nx.all_shortest_paths(ny_graph, leaf_node, root_node))
            if paths:
                return random.choice(paths)
            return nx.dijkstra_path(ny_graph, leaf_node, root_node)
        except (nx.NetworkXNoPath, nx.NetworkXError):
            return None
    
    def get_new_leaf_node(self, current_leaf):
        """获取新的叶子节点"""
        if not self.totalproviders:
            return current_leaf
            
        available_providers = [p for p in self.totalproviders if p != current_leaf]
        return random.choice(available_providers) if available_providers else current_leaf

    # 攻击  根据当前最优解进行局部调整
    def attacking(self):
        a = 2  # 收缩-扩展系数
        for i in range(self.nwhales):
            A = 2 * a * random.random() - a
            C = 2 * random.random()

            if abs(A) < 1:
                D = np.abs(C * np.array(self.best_whale['wa']) - np.array(self.whales[i]['wa']))
                self.whales[i]['wa'] = np.array(self.best_whale['wa']) - A * D
            else:
                random_whale = self.whales[random.randint(0, self.nwhales - 1)]
                D = np.abs(C * np.array(random_whale['wa']) - np.array(self.whales[i]['wa']))
                self.whales[i]['wa'] = np.array(random_whale['wa']) - A * D

            # 确保新的解仍然在合理范围内
            self.whales[i]['wa'] = np.clip(self.whales[i]['wa'], 0, 1)

            # 重新评估适应度
            self.whales[i]['fitness'] = self.fitness(self.whales[i])
            if self.whales[i]['fitness'] < self.best_fitness:
                self.best_fitness = self.whales[i]['fitness']
                self.best_whale = copy.deepcopy(self.whales[i])

#  随机搜索
    def random_search(self):
        for i in range(self.nwhales):
            for index in range(len(self.targets)):
                for j in range(self.k):
                    path_length = len(self.whales[i]['wa'][index][j])
                    random_node = random.randint(1, path_length - 2)  # 随机选择一个中间节点进行修改
                    new_node = random.randint(0, num_nodes - 1)  # 随机选择一个新的节点
                    new_path = self.whales[i]['wa'][index][j][:random_node] + [new_node] + self.whales[i]['wa'][index][
                                                                                               j][random_node + 1:]

                    # 保证路径有效性
                    if nx.has_path(ny_graph, new_path[0], new_path[-1]):
                        self.whales[i]['wa'][index][j] = new_path
                    else:
                        # 重新构建路径
                        self.whales[i]['wa'] = self.rebuild_paths(self.whales[i]['wa'])
            # 重新评估适应度
            self.whales[i]['fitness'] = self.evaluate(self.whales[i])
            if self.whales[i]['fitness'] < self.best_fitness:
                self.best_fitness = self.whales[i]['fitness']
                self.best_whale = copy.deepcopy(self.whales[i])

    def update_position(self, whale,  A, C):
        D = np.abs(C * np.array(self.best_whale['wa']) - np.array(whale['wa']))
        whale['wa'] = np.array(self.best_whale['wa']) - A * D

        # 确保新的解仍然在合理范围内
        whale['wa'] = np.clip(whale['wa'], 0, 1)

        # 重新评估适应度
        whale['fitness'] = self.evaluate(whale)
        if whale['fitness'] < self.best_fitness:
            self.best_fitness = whale['fitness']
            self.best_whale = copy.deepcopy(whale)

    def find_initial_best(self):
        for whale in self.whales:
            fitness = self.evaluate(whale)
            print(f"Evaluating whale: {whale}, fitness: {fitness}")
            if fitness < self.best_fitness:
                self.best_fitness = fitness
                self.best_whale = whale
        return self.best_whale, self.best_fitness

    # 优化开始    def optimize(self, providers, q, targets):
        """运行优化算法并记录迭代历史"""
        print("鲸鱼算法开始")
        self.fitness_history = []  # 清空迭代历史
        self.totalproviders = providers
        self.k = q
        self.targets = targets
        
        start_time = time.time()
        
        # 初始化种群
        self.whales = []
        for h in range(self.nwhales + 1):
            an = self.create_individual()
            self.whales.append(an)
        
        # 初始化最优解
        for whale in self.whales:
            if sum(whale.fitness.values) < sum(self.best_fitness):
                self.best_fitness = whale.fitness.values
                self.best_whale = copy.deepcopy(whale.wa)
        
        print(f"初始最优解：{self.best_whale}")
        
        # 主循环
        for t in range(self.max_iter):
            self.encircling()
            # 记录当前迭代的最优适应度
            self.fitness_history.append(sum(self.best_fitness))
        
        print("------------鲸鱼算法结束---------------")
        end_time = time.time()
        woatime = end_time - start_time
        print(f"woa运行时间：{woatime:.5f}秒,{woatime / 60:.5f}分钟")
        
        # 获取最终结果
        best_solution = self.whales[0].wa
        best_solution_value = self.final_evaluate(best_solution)
        
        return best_solution, best_solution_value