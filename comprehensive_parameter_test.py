#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面参数测试脚本 - 第三个问题的实现
测试6个算法在不同参数下的表现：
1. 不同带宽范围：30-300, 90-300, 150-300, 210-300, 270-300, 300-300 Mbps
2. 不同块大小：2, 4, 6, 8, 10, 12, 14, 16 MB
3. 不同RS码参数：(4,2), (6,3), (8,4), (10,5), (12,6), (14,7), (16,8), (18,9)
"""

import time
import sys
import os
import threading
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from standardTopo import NetworkConfiguration
from WOA import WhaleOptimizationAlgorithm
from PopelineWOA import PipelinedWOA
from EnhancedPipelinedWOA import EnhancedPipelinedWOA
import GAsye
from aggre_tree import AggreTree
from SRPT import SRPT

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def run_with_timeout(func, timeout_seconds=120):
    """带超时机制运行函数"""
    result = [None]
    exception = [None]

    def target():
        try:
            result[0] = func()
        except Exception as e:
            exception[0] = e

    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout_seconds)

    if thread.is_alive():
        print(f"算法运行超时 ({timeout_seconds}秒)")
        return None
    elif exception[0] is not None:
        print(f"算法运行出错: {str(exception[0])}")
        return None
    else:
        return result[0]

def run_single_algorithm(algo_name, n, k, network_config):
    """运行单个算法"""
    def run_algo():
        if algo_name == 'WOA算法':
            woa = WhaleOptimizationAlgorithm(nwhales=10, max_iter=15)  # 减少参数加速
            return woa.run(n, k, network_config)
        elif algo_name == 'PipelinedWOA算法':
            return PipelinedWOA.run(n, k, network_config)
        elif algo_name == 'EnhancedPipelinedWOA算法':
            return EnhancedPipelinedWOA.run(n, k, network_config)
        elif algo_name == 'ye_opt算法':
            ga = GAsye.GeneticAlgorithm_ye()
            return ga.run(n, k, network_config)
        elif algo_name == 'aggre算法':
            return AggreTree.run(n, k, network_config)
        elif algo_name == 'srpt算法':
            return SRPT.run(n, k, network_config)
        else:
            raise ValueError(f"未知算法: {algo_name}")

    # 设置超时时间
    timeout_map = {
        'WOA算法': 60,
        'PipelinedWOA算法': 45,
        'EnhancedPipelinedWOA算法': 30,
        'ye_opt算法': 60,
        'aggre算法': 30,
        'srpt算法': 30
    }

    timeout = timeout_map.get(algo_name, 60)
    result = run_with_timeout(run_algo, timeout_seconds=timeout)

    if result is not None:
        return result
    else:
        # 返回默认值以保持测试继续
        return {
            'transmission_delay': float('inf'),
            'flow_consumption': float('inf'),
            'std_deviation': float('inf')
        }

def test_bandwidth_ranges():
    """测试不同带宽范围"""
    print("\n" + "=" * 80)
    print("测试1: 不同带宽范围对算法性能的影响")
    print("=" * 80)

    # 带宽范围设置
    bandwidth_ranges = [
        (30, 300),   # 30-300 Mbps
        (90, 300),   # 90-300 Mbps
        (150, 300),  # 150-300 Mbps
        (210, 300),  # 210-300 Mbps
        (270, 300),  # 270-300 Mbps
        (300, 300)   # 300-300 Mbps
    ]

    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法',
                 'ye_opt算法', 'aggre算法', 'srpt算法']

    # 使用固定的RS码参数
    n, k = 14, 7

    results = {}
    for algo in algorithms:
        results[algo] = {
            'transmission_delay': [],
            'flow_consumption': [],
            'std_deviation': []
        }

    for min_bw, max_bw in bandwidth_ranges:
        print(f"\n测试带宽范围: {min_bw}-{max_bw} Mbps")

        # 创建网络配置
        network_config = NetworkConfiguration(bandwidth_range=(min_bw, max_bw))

        for algo_name in algorithms:
            print(f"  运行 {algo_name}...")
            start_time = time.time()

            result = run_single_algorithm(algo_name, n, k, network_config)

            end_time = time.time()
            runtime = end_time - start_time

            # 记录结果
            results[algo_name]['transmission_delay'].append(result['transmission_delay'])
            results[algo_name]['flow_consumption'].append(result['flow_consumption'])
            results[algo_name]['std_deviation'].append(result['std_deviation'])

            print(f"    完成 ({runtime:.2f}秒) - 时延: {result['transmission_delay']:.4f}")

    # 绘制结果
    plot_bandwidth_comparison(results, bandwidth_ranges)

    return results

def test_block_sizes():
    """测试不同块大小"""
    print("\n" + "=" * 80)
    print("测试2: 不同块大小对算法性能的影响")
    print("=" * 80)

    # 块大小设置
    block_sizes = [2, 4, 6, 8, 10, 12, 14, 16]  # MB

    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法',
                 'ye_opt算法', 'aggre算法', 'srpt算法']

    # 使用固定的RS码参数
    n, k = 14, 7

    results = {}
    for algo in algorithms:
        results[algo] = {
            'transmission_delay': [],
            'flow_consumption': [],
            'std_deviation': []
        }

    for block_size in block_sizes:
        print(f"\n测试块大小: {block_size} MB")

        # 创建网络配置
        network_config = NetworkConfiguration(block_size=block_size)

        for algo_name in algorithms:
            print(f"  运行 {algo_name}...")
            start_time = time.time()

            result = run_single_algorithm(algo_name, n, k, network_config)

            end_time = time.time()
            runtime = end_time - start_time

            # 记录结果
            results[algo_name]['transmission_delay'].append(result['transmission_delay'])
            results[algo_name]['flow_consumption'].append(result['flow_consumption'])
            results[algo_name]['std_deviation'].append(result['std_deviation'])

            print(f"    完成 ({runtime:.2f}秒) - 时延: {result['transmission_delay']:.4f}")

    # 绘制结果
    plot_block_size_comparison(results, block_sizes)

    return results

def test_rs_code_parameters():
    """测试不同RS码参数"""
    print("\n" + "=" * 80)
    print("测试3: 不同RS码参数对算法性能的影响")
    print("=" * 80)

    # RS码参数设置
    rs_params = [
        (4, 2), (6, 3), (8, 4), (10, 5),
        (12, 6), (14, 7), (16, 8), (18, 9)
    ]

    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法',
                 'ye_opt算法', 'aggre算法', 'srpt算法']

    results = {}
    for algo in algorithms:
        results[algo] = {
            'transmission_delay': [],
            'flow_consumption': [],
            'std_deviation': []
        }

    # 使用默认网络配置
    network_config = NetworkConfiguration()

    for n, k in rs_params:
        print(f"\n测试RS码参数: ({n}, {k})")

        for algo_name in algorithms:
            print(f"  运行 {algo_name}...")
            start_time = time.time()

            result = run_single_algorithm(algo_name, n, k, network_config)

            end_time = time.time()
            runtime = end_time - start_time

            # 记录结果
            results[algo_name]['transmission_delay'].append(result['transmission_delay'])
            results[algo_name]['flow_consumption'].append(result['flow_consumption'])
            results[algo_name]['std_deviation'].append(result['std_deviation'])

            print(f"    完成 ({runtime:.2f}秒) - 时延: {result['transmission_delay']:.4f}")

    # 绘制结果
    plot_rs_code_comparison(results, rs_params)

    return results

def plot_bandwidth_comparison(results, bandwidth_ranges):
    """绘制带宽范围对比图"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('不同带宽范围下的算法性能对比', fontsize=16)

    metrics = [
        ('transmission_delay', '时延'),
        ('flow_consumption', '流量消耗'),
        ('std_deviation', '负载均衡')
    ]

    x_labels = [f"{min_bw}-{max_bw}" for min_bw, max_bw in bandwidth_ranges]
    x_pos = range(len(x_labels))

    colors = {
        'EnhancedPipelinedWOA算法': '#e377c2',
        'PipelinedWOA算法': '#ff7f0e',
        'WOA算法': '#9467bd',
        'ye_opt算法': '#1f77b4',
        'aggre算法': '#2ca02c',
        'srpt算法': '#d62728'
    }

    for idx, (metric_key, metric_name) in enumerate(metrics):
        ax = axes[idx]

        for algo_name, color in colors.items():
            values = results[algo_name][metric_key]
            # 过滤无穷大值
            filtered_values = [v if v != float('inf') else np.nan for v in values]
            ax.plot(x_pos, filtered_values, 'o-', label=algo_name, color=color, linewidth=2)

        ax.set_title(metric_name, fontsize=14)
        ax.set_xlabel('带宽范围 (Mbps)', fontsize=12)
        ax.set_ylabel(metric_name, fontsize=12)
        ax.set_xticks(x_pos)
        ax.set_xticklabels(x_labels, rotation=45)
        ax.grid(True, alpha=0.3)
        ax.legend()

    plt.tight_layout()
    plt.savefig('results/bandwidth_range_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 带宽范围对比图已保存: results/bandwidth_range_comparison.png")

def plot_block_size_comparison(results, block_sizes):
    """绘制块大小对比图"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('不同块大小下的算法性能对比', fontsize=16)

    metrics = [
        ('transmission_delay', '时延'),
        ('flow_consumption', '流量消耗'),
        ('std_deviation', '负载均衡')
    ]

    colors = {
        'EnhancedPipelinedWOA算法': '#e377c2',
        'PipelinedWOA算法': '#ff7f0e',
        'WOA算法': '#9467bd',
        'ye_opt算法': '#1f77b4',
        'aggre算法': '#2ca02c',
        'srpt算法': '#d62728'
    }

    for idx, (metric_key, metric_name) in enumerate(metrics):
        ax = axes[idx]

        for algo_name, color in colors.items():
            values = results[algo_name][metric_key]
            # 过滤无穷大值
            filtered_values = [v if v != float('inf') else np.nan for v in values]
            ax.plot(block_sizes, filtered_values, 'o-', label=algo_name, color=color, linewidth=2)

        ax.set_title(metric_name, fontsize=14)
        ax.set_xlabel('块大小 (MB)', fontsize=12)
        ax.set_ylabel(metric_name, fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend()

    plt.tight_layout()
    plt.savefig('results/block_size_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 块大小对比图已保存: results/block_size_comparison.png")

def plot_rs_code_comparison(results, rs_params):
    """绘制RS码参数对比图"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('不同RS码参数下的算法性能对比', fontsize=16)

    metrics = [
        ('transmission_delay', '时延'),
        ('flow_consumption', '流量消耗'),
        ('std_deviation', '负载均衡')
    ]

    x_labels = [f"({n},{k})" for n, k in rs_params]
    x_pos = range(len(x_labels))

    colors = {
        'EnhancedPipelinedWOA算法': '#e377c2',
        'PipelinedWOA算法': '#ff7f0e',
        'WOA算法': '#9467bd',
        'ye_opt算法': '#1f77b4',
        'aggre算法': '#2ca02c',
        'srpt算法': '#d62728'
    }

    for idx, (metric_key, metric_name) in enumerate(metrics):
        ax = axes[idx]

        for algo_name, color in colors.items():
            values = results[algo_name][metric_key]
            # 过滤无穷大值
            filtered_values = [v if v != float('inf') else np.nan for v in values]
            ax.plot(x_pos, filtered_values, 'o-', label=algo_name, color=color, linewidth=2)

        ax.set_title(metric_name, fontsize=14)
        ax.set_xlabel('RS码参数 (n,k)', fontsize=12)
        ax.set_ylabel(metric_name, fontsize=12)
        ax.set_xticks(x_pos)
        ax.set_xticklabels(x_labels, rotation=45)
        ax.grid(True, alpha=0.3)
        ax.legend()

    plt.tight_layout()
    plt.savefig('results/rs_code_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ RS码参数对比图已保存: results/rs_code_comparison.png")

def generate_comprehensive_summary(bw_results, bs_results, rs_results):
    """生成综合分析报告"""
    print("\n" + "=" * 80)
    print("综合分析报告")
    print("=" * 80)

    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法',
                 'ye_opt算法', 'aggre算法', 'srpt算法']

    # 分析每个算法在不同测试中的表现
    for algo in algorithms:
        print(f"\n【{algo}】性能分析:")

        # 带宽范围测试结果
        bw_delays = [d for d in bw_results[algo]['transmission_delay'] if d != float('inf')]
        if bw_delays:
            print(f"  带宽范围测试 - 时延范围: {min(bw_delays):.4f} ~ {max(bw_delays):.4f}")

        # 块大小测试结果
        bs_delays = [d for d in bs_results[algo]['transmission_delay'] if d != float('inf')]
        if bs_delays:
            print(f"  块大小测试   - 时延范围: {min(bs_delays):.4f} ~ {max(bs_delays):.4f}")

        # RS码参数测试结果
        rs_delays = [d for d in rs_results[algo]['transmission_delay'] if d != float('inf')]
        if rs_delays:
            print(f"  RS码参数测试 - 时延范围: {min(rs_delays):.4f} ~ {max(rs_delays):.4f}")

    # 验证性能排序一致性
    print(f"\n【性能排序一致性验证】:")

    test_names = ['带宽范围', '块大小', 'RS码参数']
    test_results = [bw_results, bs_results, rs_results]

    for test_name, results in zip(test_names, test_results):
        print(f"\n{test_name}测试中的平均时延排序:")

        # 计算每个算法的平均时延
        avg_delays = {}
        for algo in algorithms:
            delays = [d for d in results[algo]['transmission_delay'] if d != float('inf')]
            if delays:
                avg_delays[algo] = np.mean(delays)
            else:
                avg_delays[algo] = float('inf')

        # 按平均时延排序
        sorted_algos = sorted(avg_delays.items(), key=lambda x: x[1])

        for i, (algo, delay) in enumerate(sorted_algos, 1):
            if delay != float('inf'):
                print(f"  {i}. {algo}: {delay:.4f}")
            else:
                print(f"  {i}. {algo}: 运行失败")

    # 生成综合对比图
    plot_comprehensive_comparison(bw_results, bs_results, rs_results)

def plot_comprehensive_comparison(bw_results, bs_results, rs_results):
    """绘制综合对比图"""
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('算法在不同参数下的综合性能对比', fontsize=16)

    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法']  # 重点关注WOA系列
    colors = ['#e377c2', '#ff7f0e', '#9467bd']

    # 1. 带宽范围对时延的影响
    ax1 = axes[0, 0]
    bandwidth_ranges = [(30, 300), (90, 300), (150, 300), (210, 300), (270, 300), (300, 300)]
    x_labels = [f"{min_bw}-{max_bw}" for min_bw, max_bw in bandwidth_ranges]

    for i, algo in enumerate(algorithms):
        delays = bw_results[algo]['transmission_delay']
        filtered_delays = [d if d != float('inf') else np.nan for d in delays]
        ax1.plot(range(len(x_labels)), filtered_delays, 'o-', label=algo, color=colors[i], linewidth=2)

    ax1.set_title('带宽范围对时延的影响', fontsize=14)
    ax1.set_xlabel('带宽范围 (Mbps)')
    ax1.set_ylabel('时延')
    ax1.set_xticks(range(len(x_labels)))
    ax1.set_xticklabels(x_labels, rotation=45)
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. 块大小对时延的影响
    ax2 = axes[0, 1]
    block_sizes = [2, 4, 6, 8, 10, 12, 14, 16]

    for i, algo in enumerate(algorithms):
        delays = bs_results[algo]['transmission_delay']
        filtered_delays = [d if d != float('inf') else np.nan for d in delays]
        ax2.plot(block_sizes, filtered_delays, 'o-', label=algo, color=colors[i], linewidth=2)

    ax2.set_title('块大小对时延的影响', fontsize=14)
    ax2.set_xlabel('块大小 (MB)')
    ax2.set_ylabel('时延')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 3. RS码参数对时延的影响
    ax3 = axes[1, 0]
    rs_params = [(4, 2), (6, 3), (8, 4), (10, 5), (12, 6), (14, 7), (16, 8), (18, 9)]
    x_labels = [f"({n},{k})" for n, k in rs_params]

    for i, algo in enumerate(algorithms):
        delays = rs_results[algo]['transmission_delay']
        filtered_delays = [d if d != float('inf') else np.nan for d in delays]
        ax3.plot(range(len(x_labels)), filtered_delays, 'o-', label=algo, color=colors[i], linewidth=2)

    ax3.set_title('RS码参数对时延的影响', fontsize=14)
    ax3.set_xlabel('RS码参数 (n,k)')
    ax3.set_ylabel('时延')
    ax3.set_xticks(range(len(x_labels)))
    ax3.set_xticklabels(x_labels, rotation=45)
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 4. 算法性能稳定性对比
    ax4 = axes[1, 1]

    # 计算每个算法在所有测试中的时延变异系数
    stability_data = {}
    for algo in algorithms:
        all_delays = []
        all_delays.extend([d for d in bw_results[algo]['transmission_delay'] if d != float('inf')])
        all_delays.extend([d for d in bs_results[algo]['transmission_delay'] if d != float('inf')])
        all_delays.extend([d for d in rs_results[algo]['transmission_delay'] if d != float('inf')])

        if all_delays:
            mean_delay = np.mean(all_delays)
            std_delay = np.std(all_delays)
            cv = std_delay / mean_delay if mean_delay > 0 else 0  # 变异系数
            stability_data[algo] = cv

    algos = list(stability_data.keys())
    cvs = list(stability_data.values())
    bars = ax4.bar(range(len(algos)), cvs, color=colors[:len(algos)], alpha=0.8)

    ax4.set_title('算法性能稳定性对比', fontsize=14)
    ax4.set_xlabel('算法')
    ax4.set_ylabel('变异系数 (越小越稳定)')
    ax4.set_xticks(range(len(algos)))
    ax4.set_xticklabels(algos, rotation=45)

    # 添加数值标签
    for bar, cv in zip(bars, cvs):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height,
                f'{cv:.3f}', ha='center', va='bottom')

    ax4.grid(True, alpha=0.3, axis='y')

    plt.tight_layout()
    plt.savefig('results/comprehensive_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 综合对比图已保存: results/comprehensive_comparison.png")

def main():
    """主函数 - 运行全面参数测试"""
    # 创建results目录
    if not os.path.exists('results'):
        os.makedirs('results')

    print("=" * 80)
    print("全面参数测试 - 第三个问题的实现")
    print("测试6个算法在不同参数下的表现情况")
    print("=" * 80)

    total_start_time = time.time()

    try:
        # 测试1: 不同带宽范围
        bw_results = test_bandwidth_ranges()

        # 测试2: 不同块大小
        bs_results = test_block_sizes()

        # 测试3: 不同RS码参数
        rs_results = test_rs_code_parameters()

        # 生成综合分析报告
        generate_comprehensive_summary(bw_results, bs_results, rs_results)

        # 计算总运行时间
        total_end_time = time.time()
        total_time = total_end_time - total_start_time

        print(f"\n" + "=" * 80)
        print("全面参数测试完成！")
        print(f"总运行时间: {total_time:.2f}秒 ({total_time/60:.2f}分钟)")
        print("\n生成的图表文件:")
        print("  - results/bandwidth_range_comparison.png")
        print("  - results/block_size_comparison.png")
        print("  - results/rs_code_comparison.png")
        print("  - results/comprehensive_comparison.png")
        print("=" * 80)

        return bw_results, bs_results, rs_results

    except Exception as e:
        print(f"测试运行出错: {str(e)}")
        raise

if __name__ == "__main__":
    try:
        bw_results, bs_results, rs_results = main()
        print("\n🎉 全面参数测试成功完成！")
    except KeyboardInterrupt:
        print("\n\n用户中断了测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
        sys.exit(1)
