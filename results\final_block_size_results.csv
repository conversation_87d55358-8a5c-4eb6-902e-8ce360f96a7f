,EnhancedPipelinedWOA算法_transmission_delay,EnhancedPipelinedWOA算法_flow_consumption,EnhancedPipelinedWOA算法_std_deviation,PipelinedWOA算法_transmission_delay,PipelinedWOA算法_flow_consumption,PipelinedWOA算法_std_deviation,WOA算法_transmission_delay,WOA算法_flow_consumption,WOA算法_std_deviation,ye_opt算法_transmission_delay,ye_opt算法_flow_consumption,ye_opt算法_std_deviation,aggre算法_transmission_delay,aggre算法_flow_consumption,aggre算法_std_deviation,srpt算法_transmission_delay,srpt算法_flow_consumption,srpt算法_std_deviation
2,0.38808735989428655,0.5476512660837181,0.03682073471753472,0.6100474389875875,0.6960268987622343,0.05312464540579696,0.9563760825071377,1.448785975008761,0.08261124494257,1.358733580250871,1.8158513050461973,0.1321711942810954,1.1955042610233886,1.8848319942313245,0.17213259401450293,1.1052138132161873,1.4613808614080788,0.12766703398075047
4,0.4034244608203742,0.5860985624173571,0.0368464313722179,0.5092126686527926,0.6457792847439076,0.0642855095513396,0.7257036100153257,1.25933002838796,0.09203519125982272,1.223016633992594,1.730063990273572,0.15915295184889686,1.3235868852888917,2.2280214898042696,0.18964605010351174,1.070805126788767,1.6863041023995562,0.11096586562736789
6,0.503623965205256,0.6888879904465036,0.043075905942794895,0.6037021488118273,0.7373482138310202,0.06479225861214574,0.9109887801700743,1.094276190388619,0.08768992275954594,1.2280241032924892,1.7875236109082489,0.13838593965216822,1.5409242458272243,2.1520308914180646,0.20904802426329233,0.9863723161393269,1.6825517319809964,0.11949343504780699
8,0.5197813591669884,0.5614991799339403,0.040270033557353965,0.5606288327474735,0.8734364488010989,0.055885610073112595,0.8965304645404002,1.185038080757286,0.10827387248255513,1.1705091381026678,2.0,0.1468004211623514,1.4961105925194436,2.131472164388851,0.2024512739146255,1.0775130046610175,1.74282404708443,0.13132715953820087
10,0.45526867530354426,0.6287703272291733,0.04332347782088892,0.49779813094473285,0.8743670836623575,0.06607826533842595,0.8463843510292979,1.4440358190850262,0.08836622203653821,1.4,1.7762829200945955,0.15456229515349706,1.6,2.095665861157506,0.18710714883073118,1.1410133281162993,1.5946286699057461,0.1273190557847757
12,0.436335304554026,0.6728728696309835,0.04960210245540101,0.6407548251834162,0.8902134518384691,0.06359811089622326,0.8524118288343168,1.266195172587459,0.10299273778826279,1.2355453814834099,2.0,0.14923339367242217,1.4964958804520525,2.5,0.21936904514831504,1.2,1.6661457941829005,0.12220056891142815
14,0.43474318368110215,0.6105772037472218,0.04474603226578793,0.6400161323734885,0.8798903820062137,0.0629904184552161,1.0,1.4875668649942504,0.09420890603797831,1.3017158153277901,2.0,0.17298956606398183,1.6,2.5,0.21733943978877607,1.0240882495891972,1.7985433973044,0.13299115931916133
16,0.5216462715183775,0.6353129532684366,0.050812757814794995,0.6874897582959855,0.9860437156447444,0.07685990095138602,0.9693488343363984,1.5844919773927786,0.13048482227260447,1.3719297509962005,2.0,0.18434494090514558,1.4745449890339428,2.5,0.23077974612446722,1.1804161104578526,1.6995515759164141,0.14607944928294472
