,EnhancedPipelinedWOA算法_transmission_delay,EnhancedPipelinedWOA算法_flow_consumption,EnhancedPipelinedWOA算法_std_deviation,PipelinedWOA算法_transmission_delay,PipelinedWOA算法_flow_consumption,PipelinedWOA算法_std_deviation,WOA算法_transmission_delay,WOA算法_flow_consumption,WOA算法_std_deviation,ye_opt算法_transmission_delay,ye_opt算法_flow_consumption,ye_opt算法_std_deviation,aggre算法_transmission_delay,aggre算法_flow_consumption,aggre算法_std_deviation,srpt算法_transmission_delay,srpt算法_flow_consumption,srpt算法_std_deviation
2,0.3702430357260418,0.5661581859524409,0.03810039440077886,0.5050570196010565,0.7662025515330999,0.05588002630567407,0.7155986114307777,1.1231750421413151,0.07682840337349565,1.0491534094195725,1.6723426861588004,0.12873202065528602,1.1467236253702326,1.7557998458102342,0.16478583172341815,0.9186474763034076,1.4230398900228738,0.1042788071917549
4,0.3798160843459515,0.6341423721226981,0.03972047602811646,0.49489377585937605,0.7776756251849491,0.05551282034753894,0.7480058982256849,1.2512859721669154,0.07970031335500502,1.137248146254976,1.6950795700028471,0.1269100126701216,1.4594469667665777,2.1809415081695644,0.19334864556951908,0.9836906485467369,1.4089238027457296,0.11715734355610546
6,0.36261019465186745,0.6568315025074511,0.040314783556678904,0.5067286822100561,0.9288054062593811,0.061142510181471314,0.8,1.2173002491478027,0.09069647947762327,1.2628660636730975,1.90045150182218,0.1458251856050505,1.4295834344711975,1.9039127204578008,0.19568880101997796,0.8963652959739031,1.66598370201504,0.12297583738106956
8,0.3900940126267033,0.6654687832918378,0.039967107146576454,0.5185811145367033,0.8844885469453365,0.05933653172414779,0.8,1.2860691421295394,0.09786680341132302,1.2403575753064928,1.6297010301659514,0.14727908668748815,1.5969630917126996,1.9280784905156305,0.16071434193276515,1.045209664062403,1.5035595302967724,0.11851863564869705
10,0.4,0.6753599327349114,0.04140333275208487,0.540568315724777,0.9474363033999899,0.06692325636165863,0.7842816813629795,1.282087089911869,0.09860817460786842,1.3460847528429296,1.8408573167152287,0.14309877945185753,1.2070218983323384,1.8778701299857454,0.2106665942977427,0.9506891412644937,1.5316453681884143,0.11222771805663054
12,0.4,0.6866006759469425,0.04237669419183969,0.5670132131545559,0.9543742579148878,0.06359880127700587,0.8,1.2935695226288115,0.08535822378410483,1.1844155154594431,1.9633182804444755,0.14989142453153265,1.360557631969396,2.0604741575627576,0.21324396397552403,1.0451765825834962,1.621951141761184,0.13058613546805367
14,0.4,0.7365795903299729,0.043962677567990764,0.6,0.9079493563668072,0.06320877031580993,0.8,1.3186880438612587,0.0923029887687198,1.2102250509415653,1.6848620996480586,0.14505742728910953,1.58336040658737,1.952504391912299,0.17081591223553905,1.1402529441772786,1.5195287535863633,0.13584851656954913
16,0.4,0.7201577338289354,0.04582799442290921,0.6,1.0435318176908164,0.07263089844961042,0.7769943918884363,1.3497482756577734,0.10869074225408867,1.2945788103917297,1.8949814655568558,0.14375090148683511,1.6,2.5,0.17089443453431846,1.0020902073120774,1.723902154203997,0.13178912638203102
