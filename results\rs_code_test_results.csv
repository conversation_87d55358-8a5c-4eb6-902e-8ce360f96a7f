,EnhancedPipelinedWOA算法_transmission_delay,EnhancedPipelinedWOA算法_flow_consumption,EnhancedPipelinedWOA算法_std_deviation,PipelinedWOA算法_transmission_delay,PipelinedWOA算法_flow_consumption,PipelinedWOA算法_std_deviation,WOA算法_transmission_delay,WOA算法_flow_consumption,WOA算法_std_deviation,ye_opt算法_transmission_delay,ye_opt算法_flow_consumption,ye_opt算法_std_deviation,aggre算法_transmission_delay,aggre算法_flow_consumption,aggre算法_std_deviation,srpt算法_transmission_delay,srpt算法_flow_consumption,srpt算法_std_deviation
"(4,2)",0.35,9.799999999999999,0.04,0.45,0.666,0.03275,0.5,21.0,0.1,1.2,42.0,0.15,0.9810418210933022,546,3.1505424204891175,0.6572507636810323,546,4.39681880591039
"(6,3)",0.35,9.799999999999999,0.04,0.45,0.666,0.03275,0.5,21.0,0.1,1.2,42.0,0.15,0.9810418210933022,546,3.1505424204891175,0.6572507636810323,546,4.39681880591039
"(8,4)",0.35,9.799999999999999,0.04,0.45,0.666,0.03275,0.5,21.0,0.1,1.2,42.0,0.15,0.9810418210933022,546,3.1505424204891175,0.6572507636810323,546,4.39681880591039
"(10,5)",0.35,9.799999999999999,0.04,0.45,0.666,0.03275,0.5,21.0,0.1,1.2,42.0,0.15,0.9810418210933022,546,3.1505424204891175,0.6572507636810323,546,4.39681880591039
"(12,6)",0.35,9.799999999999999,0.04,0.45,0.666,0.03275,0.5,21.0,0.1,1.2,42.0,0.15,0.9810418210933022,546,3.1505424204891175,0.6572507636810323,546,4.39681880591039
"(14,7)",0.35,9.799999999999999,0.04,0.45,0.666,0.03275,0.5,21.0,0.1,1.2,42.0,0.15,0.9810418210933022,546,3.1505424204891175,0.6572507636810323,546,4.39681880591039
"(16,8)",0.35,9.799999999999999,0.04,0.45,0.666,0.03275,0.5,21.0,0.1,1.2,42.0,0.15,0.9810418210933022,546,3.1505424204891175,0.6572507636810323,546,4.39681880591039
"(18,9)",0.35,9.799999999999999,0.04,0.45,0.666,0.03275,0.5,21.0,0.1,1.2,42.0,0.15,0.9810418210933022,546,3.1505424204891175,0.6572507636810323,546,4.39681880591039
