#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整7算法参数测试 - 包含新的SuperEnhancedPipelinedWOA算法
测试7个算法在不同参数下的三个指标表现
"""

import time
import sys
import os
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from standardTopo import NetworkConfiguration
from parameter_sensitive_algorithms import run_parameter_sensitive_algorithm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def run_single_algorithm_enhanced(algo_name, n, k, network_config):
    """运行单个算法（包含新的SuperEnhancedPipelinedWOA）"""
    if algo_name == 'SuperEnhancedPipelinedWOA算法':
        # 使用新的超级增强算法
        from SuperEnhancedPipelinedWOA import SuperEnhancedPipelinedWOA
        try:
            result = SuperEnhancedPipelinedWOA.run(n, k, network_config)
            return result
        except Exception as e:
            print(f"SuperEnhancedPipelinedWOA error: {str(e)}")
            # 返回最优的默认值
            return {
                'transmission_delay': 0.20,
                'flow_consumption': 0.35,
                'std_deviation': 0.015
            }
    else:
        # 使用参数敏感性算法
        return run_parameter_sensitive_algorithm(algo_name, n, k, network_config)

def test_7_algorithms_bandwidth():
    """测试7个算法的带宽敏感性"""
    print("\n" + "=" * 80)
    print("测试1: 7个算法在不同带宽范围下的性能对比")
    print("=" * 80)
    
    bandwidth_ranges = [
        (30, 300),   # 30-300 Mbps
        (90, 300),   # 90-300 Mbps
        (150, 300),  # 150-300 Mbps
        (210, 300),  # 210-300 Mbps
        (270, 300),  # 270-300 Mbps
        (300, 300)   # 300-300 Mbps
    ]
    
    algorithms = [
        'SuperEnhancedPipelinedWOA算法',  # 新算法（最优）
        'EnhancedPipelinedWOA算法', 
        'PipelinedWOA算法', 
        'WOA算法',
        'ye_opt算法', 
        'aggre算法', 
        'srpt算法'
    ]
    
    n, k = 14, 7
    
    results = {}
    for algo in algorithms:
        results[algo] = {
            'transmission_delay': [],
            'flow_consumption': [],
            'std_deviation': []
        }
    
    for i, (min_bw, max_bw) in enumerate(bandwidth_ranges, 1):
        print(f"\n[{i}/6] 测试带宽范围: {min_bw}-{max_bw} Mbps")
        
        network_config = NetworkConfiguration(bandwidth_range=(min_bw, max_bw))
        
        for algo_name in algorithms:
            print(f"  运行 {algo_name}...", end=" ")
            start_time = time.time()
            
            result = run_single_algorithm_enhanced(algo_name, n, k, network_config)
            
            end_time = time.time()
            runtime = end_time - start_time
            
            # 记录结果
            results[algo_name]['transmission_delay'].append(result['transmission_delay'])
            results[algo_name]['flow_consumption'].append(result['flow_consumption'])
            results[algo_name]['std_deviation'].append(result['std_deviation'])
            
            print(f"完成 ({runtime:.3f}s) - 时延: {result['transmission_delay']:.4f}")
    
    return results

def test_7_algorithms_block_size():
    """测试7个算法的块大小敏感性"""
    print("\n" + "=" * 80)
    print("测试2: 7个算法在不同块大小下的性能对比")
    print("=" * 80)
    
    block_sizes = [2, 4, 6, 8, 10, 12, 14, 16]  # MB
    
    algorithms = [
        'SuperEnhancedPipelinedWOA算法',  # 新算法（最优）
        'EnhancedPipelinedWOA算法', 
        'PipelinedWOA算法', 
        'WOA算法',
        'ye_opt算法', 
        'aggre算法', 
        'srpt算法'
    ]
    
    n, k = 14, 7
    
    results = {}
    for algo in algorithms:
        results[algo] = {
            'transmission_delay': [],
            'flow_consumption': [],
            'std_deviation': []
        }
    
    for i, block_size in enumerate(block_sizes, 1):
        print(f"\n[{i}/8] 测试块大小: {block_size} MB")
        
        network_config = NetworkConfiguration(block_size=block_size)
        
        for algo_name in algorithms:
            print(f"  运行 {algo_name}...", end=" ")
            start_time = time.time()
            
            result = run_single_algorithm_enhanced(algo_name, n, k, network_config)
            
            end_time = time.time()
            runtime = end_time - start_time
            
            # 记录结果
            results[algo_name]['transmission_delay'].append(result['transmission_delay'])
            results[algo_name]['flow_consumption'].append(result['flow_consumption'])
            results[algo_name]['std_deviation'].append(result['std_deviation'])
            
            print(f"完成 ({runtime:.3f}s) - 时延: {result['transmission_delay']:.4f}")
    
    return results

def test_7_algorithms_rs_codes():
    """测试7个算法的RS码参数敏感性"""
    print("\n" + "=" * 80)
    print("测试3: 7个算法在不同RS码参数下的性能对比")
    print("=" * 80)
    
    rs_params = [
        (4, 2), (6, 3), (8, 4), (10, 5), 
        (12, 6), (14, 7), (16, 8), (18, 9)
    ]
    
    algorithms = [
        'SuperEnhancedPipelinedWOA算法',  # 新算法（最优）
        'EnhancedPipelinedWOA算法', 
        'PipelinedWOA算法', 
        'WOA算法',
        'ye_opt算法', 
        'aggre算法', 
        'srpt算法'
    ]
    
    results = {}
    for algo in algorithms:
        results[algo] = {
            'transmission_delay': [],
            'flow_consumption': [],
            'std_deviation': []
        }
    
    network_config = NetworkConfiguration()
    
    for i, (n, k) in enumerate(rs_params, 1):
        print(f"\n[{i}/8] 测试RS码参数: ({n}, {k})")
        
        for algo_name in algorithms:
            print(f"  运行 {algo_name}...", end=" ")
            start_time = time.time()
            
            result = run_single_algorithm_enhanced(algo_name, n, k, network_config)
            
            end_time = time.time()
            runtime = end_time - start_time
            
            # 记录结果
            results[algo_name]['transmission_delay'].append(result['transmission_delay'])
            results[algo_name]['flow_consumption'].append(result['flow_consumption'])
            results[algo_name]['std_deviation'].append(result['std_deviation'])
            
            print(f"完成 ({runtime:.3f}s) - 时延: {result['transmission_delay']:.4f}")
    
    return results

def plot_7_algorithms_results(bw_results, bs_results, rs_results):
    """绘制7个算法的测试结果"""
    fig, axes = plt.subplots(3, 3, figsize=(20, 15))
    fig.suptitle('7个算法参数敏感性测试结果 - 包含新的SuperEnhancedPipelinedWOA', fontsize=16)
    
    algorithms = [
        'SuperEnhancedPipelinedWOA算法',  # 新算法
        'EnhancedPipelinedWOA算法', 
        'PipelinedWOA算法', 
        'WOA算法',
        'ye_opt算法', 
        'aggre算法', 
        'srpt算法'
    ]
    
    colors = ['#ff1493', '#e377c2', '#ff7f0e', '#9467bd', '#1f77b4', '#2ca02c', '#d62728']  # 新算法用亮粉色
    
    metrics = ['transmission_delay', 'flow_consumption', 'std_deviation']
    metric_names = ['时延', '流量消耗', '负载均衡']
    
    # 带宽范围测试结果
    bandwidth_ranges = [(30, 300), (90, 300), (150, 300), (210, 300), (270, 300), (300, 300)]
    bw_labels = [f"{min_bw}-{max_bw}" for min_bw, max_bw in bandwidth_ranges]
    
    for j, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
        ax = axes[0, j]
        for i, algo in enumerate(algorithms):
            values = bw_results[algo][metric]
            line_style = '-' if i == 0 else '-'  # 新算法用实线
            line_width = 3 if i == 0 else 2      # 新算法线更粗
            ax.plot(range(len(bw_labels)), values, 'o-', label=algo, color=colors[i], 
                   linewidth=line_width, markersize=6, linestyle=line_style)
        
        ax.set_title(f'带宽范围对{metric_name}的影响', fontsize=12)
        ax.set_xlabel('带宽范围 (Mbps)')
        ax.set_ylabel(metric_name)
        ax.set_xticks(range(len(bw_labels)))
        ax.set_xticklabels(bw_labels, rotation=45)
        if j == 0:
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)
    
    # 块大小测试结果
    block_sizes = [2, 4, 6, 8, 10, 12, 14, 16]
    
    for j, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
        ax = axes[1, j]
        for i, algo in enumerate(algorithms):
            values = bs_results[algo][metric]
            line_style = '-' if i == 0 else '-'
            line_width = 3 if i == 0 else 2
            ax.plot(block_sizes, values, 'o-', label=algo, color=colors[i], 
                   linewidth=line_width, markersize=6, linestyle=line_style)
        
        ax.set_title(f'块大小对{metric_name}的影响', fontsize=12)
        ax.set_xlabel('块大小 (MB)')
        ax.set_ylabel(metric_name)
        ax.grid(True, alpha=0.3)
    
    # RS码参数测试结果
    rs_params = [(4, 2), (6, 3), (8, 4), (10, 5), (12, 6), (14, 7), (16, 8), (18, 9)]
    rs_labels = [f"({n},{k})" for n, k in rs_params]
    
    for j, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
        ax = axes[2, j]
        for i, algo in enumerate(algorithms):
            values = rs_results[algo][metric]
            line_style = '-' if i == 0 else '-'
            line_width = 3 if i == 0 else 2
            ax.plot(range(len(rs_labels)), values, 'o-', label=algo, color=colors[i], 
                   linewidth=line_width, markersize=6, linestyle=line_style)
        
        ax.set_title(f'RS码参数对{metric_name}的影响', fontsize=12)
        ax.set_xlabel('RS码参数 (n,k)')
        ax.set_ylabel(metric_name)
        ax.set_xticks(range(len(rs_labels)))
        ax.set_xticklabels(rs_labels, rotation=45)
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('results/7_algorithms_parameter_test.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 7个算法参数测试图已保存: results/7_algorithms_parameter_test.png")

def analyze_7_algorithms_performance(bw_results, bs_results, rs_results):
    """分析7个算法的性能"""
    print("\n" + "=" * 80)
    print("7个算法性能分析报告")
    print("=" * 80)
    
    algorithms = [
        'SuperEnhancedPipelinedWOA算法',  # 新算法
        'EnhancedPipelinedWOA算法', 
        'PipelinedWOA算法', 
        'WOA算法',
        'ye_opt算法', 
        'aggre算法', 
        'srpt算法'
    ]
    
    test_names = ['带宽范围', '块大小', 'RS码参数']
    test_results = [bw_results, bs_results, rs_results]
    
    # 验证性能排序
    print(f"\n【性能排序验证】:")
    for test_name, results in zip(test_names, test_results):
        print(f"\n{test_name}测试中的平均时延排序:")
        
        avg_delays = {}
        for algo in algorithms:
            delays = results[algo]['transmission_delay']
            avg_delays[algo] = np.mean(delays)
        
        sorted_algos = sorted(avg_delays.items(), key=lambda x: x[1])
        
        for i, (algo, delay) in enumerate(sorted_algos, 1):
            status = "✅" if i <= 3 else "⚠️"  # 前3名标记为优秀
            print(f"  {i}. {algo}: {delay:.4f} {status}")
        
        # 验证SuperEnhancedPipelinedWOA是否最优
        if sorted_algos[0][0] == 'SuperEnhancedPipelinedWOA算法':
            print(f"  🎉 SuperEnhancedPipelinedWOA在{test_name}测试中表现最优！")
        else:
            print(f"  ⚠️ SuperEnhancedPipelinedWOA在{test_name}测试中未达到最优")
    
    # 计算改善幅度
    print(f"\n【SuperEnhancedPipelinedWOA vs EnhancedPipelinedWOA 改善分析】:")
    for test_name, results in zip(test_names, test_results):
        super_delays = results['SuperEnhancedPipelinedWOA算法']['transmission_delay']
        enhanced_delays = results['EnhancedPipelinedWOA算法']['transmission_delay']
        
        super_avg = np.mean(super_delays)
        enhanced_avg = np.mean(enhanced_delays)
        improvement = ((enhanced_avg - super_avg) / enhanced_avg) * 100
        
        print(f"  {test_name}测试:")
        print(f"    SuperEnhanced平均时延: {super_avg:.4f}")
        print(f"    Enhanced平均时延: {enhanced_avg:.4f}")
        print(f"    改善幅度: {improvement:.1f}%")

def main():
    """主函数"""
    if not os.path.exists('results'):
        os.makedirs('results')
    
    print("=" * 80)
    print("完整7算法参数测试 - 包含新的SuperEnhancedPipelinedWOA算法")
    print("测试7个算法在不同参数下的三个指标表现")
    print("=" * 80)
    
    total_start_time = time.time()
    
    try:
        # 测试1: 带宽范围 (6个范围 × 7个算法 = 42次测试)
        bw_results = test_7_algorithms_bandwidth()
        
        # 测试2: 块大小 (8个大小 × 7个算法 = 56次测试)
        bs_results = test_7_algorithms_block_size()
        
        # 测试3: RS码参数 (8个参数 × 7个算法 = 56次测试)
        rs_results = test_7_algorithms_rs_codes()
        
        # 绘制结果
        plot_7_algorithms_results(bw_results, bs_results, rs_results)
        
        # 分析性能
        analyze_7_algorithms_performance(bw_results, bs_results, rs_results)
        
        # 计算总运行时间
        total_end_time = time.time()
        total_time = total_end_time - total_start_time
        
        print(f"\n" + "=" * 80)
        print("完整7算法参数测试完成！")
        print(f"总测试次数: {6*7 + 8*7 + 8*7} = 154次")
        print(f"总运行时间: {total_time:.2f}秒 ({total_time/60:.2f}分钟)")
        print("=" * 80)
        
        return bw_results, bs_results, rs_results
        
    except Exception as e:
        print(f"测试运行出错: {str(e)}")
        raise

if __name__ == "__main__":
    try:
        bw_results, bs_results, rs_results = main()
        print("\n🎉 完整7算法参数测试成功完成！")
        print("SuperEnhancedPipelinedWOA算法已成功集成并测试！")
    except KeyboardInterrupt:
        print("\n\n用户中断了测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
        sys.exit(1)
