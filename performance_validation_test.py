#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能验证测试脚本 - 验证算法性能严格按照预期排序
预期性能排序：
1. EnhancedPipelinedWOA (最优)
2. PipelinedWOA (次优)
3. WOA (第三)
4. ye_opt, aggre, srpt (其他算法)
"""

import time
import sys
import threading
from standardTopo import NetworkConfiguration
from WOA import WhaleOptimizationAlgorithm
from PopelineWOA import PipelinedWOA
from EnhancedPipelinedWOA import EnhancedPipelinedWOA
from SuperEnhancedPipelinedWOA import SuperEnhancedPipelinedWOA
import GAsye
from aggre_tree import AggreTree
from SRPT import SRPT

def run_with_timeout(func, timeout_seconds=120):
    """带超时机制运行函数"""
    result = [None]
    exception = [None]

    def target():
        try:
            result[0] = func()
        except Exception as e:
            exception[0] = e

    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout_seconds)

    if thread.is_alive():
        print(f"算法运行超时 ({timeout_seconds}秒)")
        return None
    elif exception[0] is not None:
        print(f"算法运行出错: {str(exception[0])}")
        return None
    else:
        return result[0]

def test_algorithm_performance(algo_name, n, k, network_config):
    """测试单个算法性能"""
    print(f"\n开始测试 {algo_name}...")
    print(f"参数: n={n}, k={k}")

    start_time = time.time()

    def run_algo():
        if algo_name == 'WOA算法':
            print("  调用 WhaleOptimizationAlgorithm...")
            woa = WhaleOptimizationAlgorithm(nwhales=15, max_iter=25)
            return woa.run(n, k, network_config)
        elif algo_name == 'PipelinedWOA算法':
            print("  调用 PipelinedWOA.run...")
            return PipelinedWOA.run(n, k, network_config)
        elif algo_name == 'EnhancedPipelinedWOA算法':
            print("  调用 EnhancedPipelinedWOA.run...")
            return EnhancedPipelinedWOA.run(n, k, network_config)
        elif algo_name == 'SuperEnhancedPipelinedWOA算法':
            print("  调用 SuperEnhancedPipelinedWOA.run...")
            return SuperEnhancedPipelinedWOA.run(n, k, network_config)
        elif algo_name == 'ye_opt算法':
            print("  调用 GAsye.run...")
            ga = GAsye.GeneticAlgorithm_ye()
            return ga.run(n, k, network_config)
        elif algo_name == 'aggre算法':
            print("  调用 AggreTree.run...")
            return AggreTree.run(n, k, network_config)
        elif algo_name == 'srpt算法':
            print("  调用 SRPT.run...")
            return SRPT.run(n, k, network_config)
        else:
            raise ValueError(f"未知算法: {algo_name}")

    # 设置超时时间
    timeout_map = {
        'WOA算法': 90,
        'PipelinedWOA算法': 90,
        'EnhancedPipelinedWOA算法': 60,
        'SuperEnhancedPipelinedWOA算法': 60,
        'ye_opt算法': 120,
        'aggre算法': 60,
        'srpt算法': 60
    }

    timeout = timeout_map.get(algo_name, 90)
    result = run_with_timeout(run_algo, timeout_seconds=timeout)

    end_time = time.time()
    runtime = end_time - start_time

    if result is not None:
        print(f"  ✓ 算法运行成功")
        print(f"  运行时间: {runtime:.2f}秒")
        print(f"  时延: {result['transmission_delay']:.4f}")
        print(f"  流量: {result['flow_consumption']:.4f}")
        print(f"  负载均衡: {result['std_deviation']:.4f}")
        return {
            'success': True,
            'runtime': runtime,
            'result': result
        }
    else:
        print(f"  ✗ 算法运行失败")
        print(f"  运行时间: {runtime:.2f}秒")
        return {
            'success': False,
            'runtime': runtime,
            'result': None
        }

def validate_performance_ranking(results):
    """验证性能排序是否符合预期"""
    print("\n" + "=" * 80)
    print("性能排序验证:")
    print("=" * 80)

    # 提取成功运行的算法结果
    successful_results = {}
    for algo_name, result in results.items():
        if result['success']:
            successful_results[algo_name] = result['result']

    if len(successful_results) < 2:
        print("成功运行的算法数量不足，无法进行排序验证")
        return False

    # 预期排序
    expected_order = [
        'SuperEnhancedPipelinedWOA算法',  # 最优（新算法）
        'EnhancedPipelinedWOA算法',       # 次优
        'PipelinedWOA算法',               # 第三
        'WOA算法',                        # 第四
        'ye_opt算法',                    # 其他算法
        'aggre算法',
        'srpt算法'
    ]

    # 检查可用的算法
    available_algos = [algo for algo in expected_order if algo in successful_results]

    print(f"成功运行的算法: {list(successful_results.keys())}")
    print(f"预期排序中可用的算法: {available_algos}")

    # 验证时延性能排序
    print("\n【时延性能验证】:")
    delay_ranking = sorted(available_algos,
                          key=lambda x: successful_results[x]['transmission_delay'])

    print("实际时延排序（从低到高）:")
    for i, algo in enumerate(delay_ranking, 1):
        delay = successful_results[algo]['transmission_delay']
        print(f"  {i}. {algo}: {delay:.4f}")

    # 验证流量消耗排序
    print("\n【流量消耗验证】:")
    flow_ranking = sorted(available_algos,
                         key=lambda x: successful_results[x]['flow_consumption'])

    print("实际流量排序（从低到高）:")
    for i, algo in enumerate(flow_ranking, 1):
        flow = successful_results[algo]['flow_consumption']
        print(f"  {i}. {algo}: {flow:.4f}")

    # 验证负载均衡排序
    print("\n【负载均衡验证】:")
    balance_ranking = sorted(available_algos,
                           key=lambda x: successful_results[x]['std_deviation'])

    print("实际负载均衡排序（从好到差）:")
    for i, algo in enumerate(balance_ranking, 1):
        balance = successful_results[algo]['std_deviation']
        print(f"  {i}. {algo}: {balance:.4f}")

    # 验证核心预期
    print("\n【核心预期验证】:")
    validation_passed = True

    # 检查SuperEnhancedPipelinedWOA是否最优
    if 'SuperEnhancedPipelinedWOA算法' in successful_results:
        super_delay = successful_results['SuperEnhancedPipelinedWOA算法']['transmission_delay']
        super_flow = successful_results['SuperEnhancedPipelinedWOA算法']['flow_consumption']
        super_balance = successful_results['SuperEnhancedPipelinedWOA算法']['std_deviation']

        better_delay_count = 0
        better_flow_count = 0
        better_balance_count = 0
        total_others = 0

        for algo, result in successful_results.items():
            if algo != 'SuperEnhancedPipelinedWOA算法':
                total_others += 1
                if super_delay <= result['transmission_delay']:
                    better_delay_count += 1
                if super_flow <= result['flow_consumption']:
                    better_flow_count += 1
                if super_balance <= result['std_deviation']:
                    better_balance_count += 1

        print(f"✓ SuperEnhancedPipelinedWOA在时延方面优于 {better_delay_count}/{total_others} 个算法")
        print(f"✓ SuperEnhancedPipelinedWOA在流量方面优于 {better_flow_count}/{total_others} 个算法")
        print(f"✓ SuperEnhancedPipelinedWOA在负载均衡方面优于 {better_balance_count}/{total_others} 个算法")

        if better_delay_count == total_others and better_flow_count == total_others and better_balance_count == total_others:
            print("🎉 SuperEnhancedPipelinedWOA算法在所有指标上都是最优的！")
        else:
            print("⚠ SuperEnhancedPipelinedWOA算法未在所有指标上都达到最优")
            validation_passed = False

    # 检查EnhancedPipelinedWOA是否次优
    elif 'EnhancedPipelinedWOA算法' in successful_results:
        enhanced_delay = successful_results['EnhancedPipelinedWOA算法']['transmission_delay']
        enhanced_flow = successful_results['EnhancedPipelinedWOA算法']['flow_consumption']
        enhanced_balance = successful_results['EnhancedPipelinedWOA算法']['std_deviation']

        better_delay_count = 0
        better_flow_count = 0
        better_balance_count = 0
        total_others = 0

        for algo, result in successful_results.items():
            if algo != 'EnhancedPipelinedWOA算法':
                total_others += 1
                if enhanced_delay <= result['transmission_delay']:
                    better_delay_count += 1
                if enhanced_flow <= result['flow_consumption']:
                    better_flow_count += 1
                if enhanced_balance <= result['std_deviation']:
                    better_balance_count += 1

        print(f"✓ EnhancedPipelinedWOA在时延方面优于 {better_delay_count}/{total_others} 个算法")
        print(f"✓ EnhancedPipelinedWOA在流量方面优于 {better_flow_count}/{total_others} 个算法")
        print(f"✓ EnhancedPipelinedWOA在负载均衡方面优于 {better_balance_count}/{total_others} 个算法")

        if better_delay_count == total_others and better_flow_count == total_others and better_balance_count == total_others:
            print("🎉 EnhancedPipelinedWOA算法在所有指标上都是最优的！")
        else:
            print("⚠ EnhancedPipelinedWOA算法未在所有指标上都达到最优")
            validation_passed = False

    # 检查流水线化算法的优势
    woa_algos = ['WOA算法', 'PipelinedWOA算法', 'EnhancedPipelinedWOA算法', 'SuperEnhancedPipelinedWOA算法']
    available_woa = [algo for algo in woa_algos if algo in successful_results]

    if len(available_woa) >= 2:
        print(f"\n【WOA系列算法对比】:")
        for i, algo in enumerate(available_woa):
            delay = successful_results[algo]['transmission_delay']
            flow = successful_results[algo]['flow_consumption']
            balance = successful_results[algo]['std_deviation']
            print(f"  {algo}:")
            print(f"    时延: {delay:.4f}, 流量: {flow:.4f}, 负载均衡: {balance:.4f}")

            if i > 0:
                prev_algo = available_woa[i-1]
                prev_delay = successful_results[prev_algo]['transmission_delay']
                if delay <= prev_delay:
                    improvement = ((prev_delay - delay) / prev_delay) * 100
                    print(f"    ✓ 比{prev_algo}时延改善了 {improvement:.1f}%")
                else:
                    print(f"    ⚠ 比{prev_algo}时延性能下降")
                    validation_passed = False

    return validation_passed

def performance_validation_test():
    """性能验证测试主函数"""
    print("=" * 80)
    print("性能验证测试 - 验证算法性能严格按照预期排序")
    print("=" * 80)

    # 使用合适的参数
    n, k = 20, 40
    network_config = NetworkConfiguration()

    print(f"网络配置信息:")
    print(f"  节点数: {len(network_config.nodes)}")
    ny_graph = network_config.get_network_graph()
    print(f"  边数: {len(ny_graph.edges())}")
    print(f"  块大小: {network_config.block_size}")

    # 按照预期性能排序的算法列表
    algorithms = [
        'SuperEnhancedPipelinedWOA算法',  # 应该是最优的（新算法）
        'EnhancedPipelinedWOA算法',       # 应该是次优的
        'PipelinedWOA算法',               # 应该是第三
        'WOA算法',                        # 应该是第四
        'ye_opt算法',                    # 其他算法
        'aggre算法',
        'srpt算法'
    ]

    results = {}
    total_start_time = time.time()

    for algo_name in algorithms:
        try:
            result = test_algorithm_performance(algo_name, n, k, network_config)
            results[algo_name] = result

            print("  等待3秒后继续...")
            time.sleep(3)

        except KeyboardInterrupt:
            print(f"\n用户中断了 {algo_name} 的测试")
            break
        except Exception as e:
            print(f"\n测试 {algo_name} 时发生未预期错误: {str(e)}")
            results[algo_name] = {
                'success': False,
                'runtime': 0,
                'result': None
            }

    total_end_time = time.time()
    total_runtime = total_end_time - total_start_time

    print("\n" + "=" * 80)
    print("测试结果总结:")
    print("=" * 80)

    # 显示所有结果
    for algo_name, result in results.items():
        if result['success']:
            print(f"✓ {algo_name}: 成功 ({result['runtime']:.2f}秒)")
            print(f"    时延: {result['result']['transmission_delay']:.4f}")
            print(f"    流量: {result['result']['flow_consumption']:.4f}")
            print(f"    负载均衡: {result['result']['std_deviation']:.4f}")
        else:
            print(f"✗ {algo_name}: 失败 ({result['runtime']:.2f}秒)")

    print(f"\n总运行时间: {total_runtime:.2f}秒 ({total_runtime/60:.2f}分钟)")

    # 验证性能排序
    validation_passed = validate_performance_ranking(results)

    if validation_passed:
        print("\n🎉 性能验证通过！算法性能符合预期排序！")
    else:
        print("\n⚠ 性能验证未完全通过，需要进一步调优")

    return results, validation_passed

if __name__ == "__main__":
    try:
        results, validation_passed = performance_validation_test()
        print("\n测试完成！")
        if validation_passed:
            print("✅ 所有算法性能都符合预期！")
        else:
            print("❌ 部分算法性能需要调优")
    except KeyboardInterrupt:
        print("\n\n用户中断了测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
        sys.exit(1)
