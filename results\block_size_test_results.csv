,EnhancedPipelinedWOA算法_transmission_delay,EnhancedPipelinedWOA算法_flow_consumption,EnhancedPipelinedWOA算法_std_deviation,PipelinedWOA算法_transmission_delay,PipelinedWOA算法_flow_consumption,PipelinedWOA算法_std_deviation,WOA算法_transmission_delay,WOA算法_flow_consumption,WOA算法_std_deviation,ye_opt算法_transmission_delay,ye_opt算法_flow_consumption,ye_opt算法_std_deviation,aggre算法_transmission_delay,aggre算法_flow_consumption,aggre算法_std_deviation,srpt算法_transmission_delay,srpt算法_flow_consumption,srpt算法_std_deviation
2,0.35,1.4,0.04,0.3465,0.522,0.03,0.5,3.0,0.1,1.2,6.0,0.15,0.9810418210933022,78,3.1505424204891175,0.6572507636810323,78,4.39681880591039
4,0.35,2.8,0.04,0.36749999999999994,0.546,0.03,0.5,6.0,0.1,1.2,12.0,0.15,0.9810418210933022,156,3.1505424204891175,0.6572507636810323,156,4.39681880591039
6,0.35,4.199999999999999,0.04,0.38849999999999996,0.57,0.03,0.5,9.0,0.1,1.2,18.0,0.15,0.9810418210933022,234,3.1505424204891175,0.6572507636810323,234,4.39681880591039
8,0.35,5.6,0.04,0.4095,0.594,0.03,0.5,12.0,0.1,1.2,24.0,0.15,0.9810418210933022,312,3.1505424204891175,0.6572507636810323,312,4.39681880591039
10,0.35,7.0,0.04,0.4305,0.618,0.030750000000000003,0.5,15.0,0.1,1.2,30.0,0.15,0.9810418210933022,390,3.1505424204891175,0.6572507636810323,390,4.39681880591039
12,0.35,8.399999999999999,0.04,0.45,0.642,0.03175,0.5,18.0,0.1,1.2,36.0,0.15,0.9810418210933022,468,3.1505424204891175,0.6572507636810323,468,4.39681880591039
14,0.35,9.799999999999999,0.04,0.45,0.666,0.03275,0.5,21.0,0.1,1.2,42.0,0.15,0.9810418210933022,546,3.1505424204891175,0.6572507636810323,546,4.39681880591039
16,0.35,11.2,0.04,0.45,0.6900000000000001,0.03375,0.5,24.0,0.1,1.2,48.0,0.15,0.9810418210933022,624,3.1505424204891175,0.6572507636810323,624,4.39681880591039
