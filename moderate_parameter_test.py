#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
适中参数测试 - 第三个问题的平衡版本
在运行时间和测试完整性之间取得平衡
"""

import time
import sys
import os
import threading
import matplotlib.pyplot as plt
import numpy as np
from standardTopo import NetworkConfiguration
from WOA import WhaleOptimizationAlgorithm
from PopelineWOA import PipelinedWOA
from EnhancedPipelinedWOA import EnhancedPipelinedWOA
from SuperEnhancedPipelinedWOA import SuperEnhancedPipelinedWOA
import GAsye
from aggre_tree import AggreTree
from SRPT import SRPT

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def run_with_timeout(func, timeout_seconds=90):
    """带超时机制运行函数"""
    result = [None]
    exception = [None]

    def target():
        try:
            result[0] = func()
        except Exception as e:
            exception[0] = e

    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout_seconds)

    if thread.is_alive():
        print(f"超时 ({timeout_seconds}秒)")
        return None
    elif exception[0] is not None:
        print(f"出错: {str(exception[0])}")
        return None
    else:
        return result[0]

def run_single_algorithm(algo_name, n, k, network_config):
    """运行单个算法"""
    def run_algo():
        if algo_name == 'WOA算法':
            woa = WhaleOptimizationAlgorithm(nwhales=12, max_iter=20)  # 适中参数
            return woa.run(n, k, network_config)
        elif algo_name == 'PipelinedWOA算法':
            return PipelinedWOA.run(n, k, network_config)
        elif algo_name == 'EnhancedPipelinedWOA算法':
            return EnhancedPipelinedWOA.run(n, k, network_config)
        elif algo_name == 'SuperEnhancedPipelinedWOA算法':
            return SuperEnhancedPipelinedWOA.run(n, k, network_config)
        elif algo_name == 'ye_opt算法':
            ga = GAsye.GeneticAlgorithm_ye()
            return ga.run(n, k, network_config)
        elif algo_name == 'aggre算法':
            return AggreTree.run(n, k, network_config)
        elif algo_name == 'srpt算法':
            return SRPT.run(n, k, network_config)
        else:
            raise ValueError(f"未知算法: {algo_name}")

    timeout_map = {
        'WOA算法': 60,
        'PipelinedWOA算法': 45,
        'EnhancedPipelinedWOA算法': 30,
        'ye_opt算法': 60,
        'aggre算法': 30,
        'srpt算法': 30
    }

    timeout = timeout_map.get(algo_name, 60)
    result = run_with_timeout(run_algo, timeout_seconds=timeout)

    if result is not None:
        return result
    else:
        return {
            'transmission_delay': float('inf'),
            'flow_consumption': float('inf'),
            'std_deviation': float('inf')
        }

def test_bandwidth_ranges():
    """测试带宽范围（选择5个范围）"""
    print("\n" + "=" * 80)
    print("测试1: 不同带宽范围对算法性能的影响")
    print("=" * 80)

    # 选择5个代表性带宽范围
    bandwidth_ranges = [
        (30, 300),   # 最大范围
        (90, 300),   # 较大范围
        (150, 300),  # 中等范围
        (210, 300),  # 较小范围
        (300, 300)   # 固定带宽
    ]

    algorithms = ['SuperEnhancedPipelinedWOA算法', 'EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法']

    # 使用适中的RS码参数
    n, k = 14, 7

    results = {}
    for algo in algorithms:
        results[algo] = {
            'transmission_delay': [],
            'flow_consumption': [],
            'std_deviation': []
        }

    for min_bw, max_bw in bandwidth_ranges:
        print(f"\n测试带宽范围: {min_bw}-{max_bw} Mbps")

        network_config = NetworkConfiguration(bandwidth_range=(min_bw, max_bw))

        for algo_name in algorithms:
            print(f"  运行 {algo_name}...", end=" ")
            start_time = time.time()

            result = run_single_algorithm(algo_name, n, k, network_config)

            end_time = time.time()
            runtime = end_time - start_time

            # 记录结果
            results[algo_name]['transmission_delay'].append(result['transmission_delay'])
            results[algo_name]['flow_consumption'].append(result['flow_consumption'])
            results[algo_name]['std_deviation'].append(result['std_deviation'])

            if result['transmission_delay'] != float('inf'):
                print(f"完成 ({runtime:.1f}s) - 时延: {result['transmission_delay']:.4f}")
            else:
                print(f"失败 ({runtime:.1f}s)")

    return results

def test_block_sizes():
    """测试块大小（选择6个大小）"""
    print("\n" + "=" * 80)
    print("测试2: 不同块大小对算法性能的影响")
    print("=" * 80)

    # 选择6个代表性块大小
    block_sizes = [2, 6, 10, 14, 16]  # MB

    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法',
                 'ye_opt算法', 'aggre算法', 'srpt算法']

    # 使用适中的RS码参数
    n, k = 14, 7

    results = {}
    for algo in algorithms:
        results[algo] = {
            'transmission_delay': [],
            'flow_consumption': [],
            'std_deviation': []
        }

    for block_size in block_sizes:
        print(f"\n测试块大小: {block_size} MB")

        network_config = NetworkConfiguration(block_size=block_size)

        for algo_name in algorithms:
            print(f"  运行 {algo_name}...", end=" ")
            start_time = time.time()

            result = run_single_algorithm(algo_name, n, k, network_config)

            end_time = time.time()
            runtime = end_time - start_time

            # 记录结果
            results[algo_name]['transmission_delay'].append(result['transmission_delay'])
            results[algo_name]['flow_consumption'].append(result['flow_consumption'])
            results[algo_name]['std_deviation'].append(result['std_deviation'])

            if result['transmission_delay'] != float('inf'):
                print(f"完成 ({runtime:.1f}s) - 时延: {result['transmission_delay']:.4f}")
            else:
                print(f"失败 ({runtime:.1f}s)")

    return results

def test_rs_parameters():
    """测试RS码参数（选择6个参数）"""
    print("\n" + "=" * 80)
    print("测试3: 不同RS码参数对算法性能的影响")
    print("=" * 80)

    # 选择6个代表性RS码参数
    rs_params = [(4, 2), (8, 4), (12, 6), (14, 7), (16, 8), (18, 9)]

    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法',
                 'ye_opt算法', 'aggre算法', 'srpt算法']

    results = {}
    for algo in algorithms:
        results[algo] = {
            'transmission_delay': [],
            'flow_consumption': [],
            'std_deviation': []
        }

    network_config = NetworkConfiguration()

    for n, k in rs_params:
        print(f"\n测试RS码参数: ({n}, {k})")

        for algo_name in algorithms:
            print(f"  运行 {algo_name}...", end=" ")
            start_time = time.time()

            result = run_single_algorithm(algo_name, n, k, network_config)

            end_time = time.time()
            runtime = end_time - start_time

            # 记录结果
            results[algo_name]['transmission_delay'].append(result['transmission_delay'])
            results[algo_name]['flow_consumption'].append(result['flow_consumption'])
            results[algo_name]['std_deviation'].append(result['std_deviation'])

            if result['transmission_delay'] != float('inf'):
                print(f"完成 ({runtime:.1f}s) - 时延: {result['transmission_delay']:.4f}")
            else:
                print(f"失败 ({runtime:.1f}s)")

    return results

def plot_moderate_results(bw_results, bs_results, rs_results):
    """绘制3x3子图 - 展示不同参数下各算法各性能变化"""
    # 创建3x3子图
    fig, axes = plt.subplots(3, 3, figsize=(18, 15))
    fig.suptitle('算法性能参数敏感性分析 (3x3子图)', fontsize=16)

    # 算法和颜色设置
    algorithms = ['SuperEnhancedPipelinedWOA算法', 'EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法']
    colors = ['#ff1493', '#e377c2', '#ff7f0e', '#9467bd']

    # 参数设置
    bandwidth_ranges = [(30, 300), (90, 300), (150, 300), (210, 300), (300, 300)]
    bw_labels = [f"{min_bw}-{max_bw}" for min_bw, max_bw in bandwidth_ranges]

    block_sizes = [2, 6, 10, 14, 16]
    bs_labels = [f"{bs}MB" for bs in block_sizes]

    rs_params = [(4, 2), (8, 4), (12, 6), (14, 7), (16, 8), (18, 9)]
    rs_labels = [f"({n},{k})" for n, k in rs_params]

    # 性能指标
    metrics = ['transmission_delay', 'flow_consumption', 'std_deviation']
    metric_names = ['传输时延', '流量消耗', '负载均衡']

    # 绘制3x3子图
    for row, (results, labels, param_name) in enumerate([
        (bw_results, bw_labels, '带宽参数'),
        (bs_results, bs_labels, '块大小参数'),
        (rs_results, rs_labels, 'RS码参数')
    ]):
        for col, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
            ax = axes[row, col]

            # 绘制每个算法的曲线
            for i, algo in enumerate(algorithms):
                if algo in results:
                    values = results[algo][metric]
                    filtered_values = [v if v != float('inf') else np.nan for v in values]
                    ax.plot(range(len(labels)), filtered_values,
                           label=algo, color=colors[i],
                           marker='o', linewidth=2, markersize=6)

            # 设置标题和标签
            ax.set_title(f'{param_name} - {metric_name}', fontsize=12)
            ax.set_xlabel('参数值', fontsize=10)
            ax.set_ylabel(metric_name, fontsize=10)
            ax.set_xticks(range(len(labels)))
            ax.set_xticklabels(labels, rotation=45, fontsize=8)

            # 添加网格
            ax.grid(True, alpha=0.3)

            # 只在第一行第一列添加图例
            if row == 0 and col == 0:
                ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)

    plt.tight_layout()
    plt.savefig('results/parameter_sensitivity_3x3.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✅ 3x3参数敏感性图已保存: results/parameter_sensitivity_3x3.png")

def generate_analysis_report(bw_results, bs_results, rs_results):
    """生成分析报告"""
    print("\n" + "=" * 80)
    print("详细分析报告")
    print("=" * 80)

    algorithms = ['EnhancedPipelinedWOA算法', 'PipelinedWOA算法', 'WOA算法',
                 'ye_opt算法', 'aggre算法', 'srpt算法']

    # 分析每个算法在不同测试中的表现
    for algo in algorithms:
        print(f"\n【{algo}】性能分析:")

        # 带宽范围测试结果
        bw_delays = [d for d in bw_results[algo]['transmission_delay'] if d != float('inf')]
        if bw_delays:
            print(f"  带宽范围测试 - 时延范围: {min(bw_delays):.4f} ~ {max(bw_delays):.4f}, 平均: {np.mean(bw_delays):.4f}")

        # 块大小测试结果
        bs_delays = [d for d in bs_results[algo]['transmission_delay'] if d != float('inf')]
        if bs_delays:
            print(f"  块大小测试   - 时延范围: {min(bs_delays):.4f} ~ {max(bs_delays):.4f}, 平均: {np.mean(bs_delays):.4f}")

        # RS码参数测试结果
        rs_delays = [d for d in rs_results[algo]['transmission_delay'] if d != float('inf')]
        if rs_delays:
            print(f"  RS码参数测试 - 时延范围: {min(rs_delays):.4f} ~ {max(rs_delays):.4f}, 平均: {np.mean(rs_delays):.4f}")

    # 验证性能排序一致性
    print(f"\n【性能排序一致性验证】:")

    test_names = ['带宽范围', '块大小', 'RS码参数']
    test_results = [bw_results, bs_results, rs_results]

    for test_name, results in zip(test_names, test_results):
        print(f"\n{test_name}测试中的平均时延排序:")

        # 计算每个算法的平均时延
        avg_delays = {}
        for algo in algorithms:
            delays = [d for d in results[algo]['transmission_delay'] if d != float('inf')]
            if delays:
                avg_delays[algo] = np.mean(delays)
            else:
                avg_delays[algo] = float('inf')

        # 按平均时延排序
        sorted_algos = sorted(avg_delays.items(), key=lambda x: x[1])

        for i, (algo, delay) in enumerate(sorted_algos, 1):
            if delay != float('inf'):
                print(f"  {i}. {algo}: {delay:.4f}")
            else:
                print(f"  {i}. {algo}: 运行失败")

def main():
    """主函数 - 运行适中参数测试"""
    if not os.path.exists('results'):
        os.makedirs('results')

    print("=" * 80)
    print("适中参数测试 - 第三个问题的平衡实现")
    print("在运行时间和测试完整性之间取得平衡")
    print("=" * 80)

    total_start_time = time.time()

    try:
        # 测试1: 带宽范围
        bw_results = test_bandwidth_ranges()

        # 测试2: 块大小
        bs_results = test_block_sizes()

        # 测试3: RS码参数
        rs_results = test_rs_parameters()

        # 绘制结果
        plot_moderate_results(bw_results, bs_results, rs_results)

        # 生成分析报告
        generate_analysis_report(bw_results, bs_results, rs_results)

        # 计算总运行时间
        total_end_time = time.time()
        total_time = total_end_time - total_start_time

        print(f"\n" + "=" * 80)
        print("适中参数测试完成！")
        print(f"总运行时间: {total_time:.2f}秒 ({total_time/60:.2f}分钟)")
        print("\n生成的图表文件:")
        print("  - results/moderate_parameter_test.png")
        print("=" * 80)

        return bw_results, bs_results, rs_results

    except Exception as e:
        print(f"测试运行出错: {str(e)}")
        raise

if __name__ == "__main__":
    try:
        bw_results, bs_results, rs_results = main()
        print("\n🎉 适中参数测试成功完成！")
        print("如需更完整的测试，可以运行 comprehensive_parameter_test.py")
    except KeyboardInterrupt:
        print("\n\n用户中断了测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
        sys.exit(1)
