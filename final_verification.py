#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证 - 确保SuperEnhanced稳定超越Enhanced
"""

import matplotlib.pyplot as plt
import numpy as np
import os
from standardTopo import NetworkConfiguration
from EnhancedPipelinedWOA import EnhancedPipelinedWOA
from SuperEnhancedPipelinedWOA import SuperEnhancedPipelinedWOA

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def final_verification_test():
    """最终验证测试"""
    print("=" * 80)
    print("最终验证：SuperEnhanced vs Enhanced")
    print("=" * 80)
    
    # 测试多个场景
    test_scenarios = [
        {'name': '场景1', 'n': 6, 'k': 3},
        {'name': '场景2', 'n': 8, 'k': 4},
        {'name': '场景3', 'n': 10, 'k': 5},
        {'name': '场景4', 'n': 12, 'k': 6},
        {'name': '场景5', 'n': 14, 'k': 7}
    ]
    
    network_config = NetworkConfiguration()
    
    enhanced_results = []
    super_results = []
    
    for scenario in test_scenarios:
        print(f"\n测试 {scenario['name']} - RS码({scenario['n']}, {scenario['k']})")
        
        # 测试Enhanced
        print("  运行 EnhancedPipelinedWOA...")
        enhanced_result = EnhancedPipelinedWOA.run(scenario['n'], scenario['k'], network_config)
        enhanced_results.append(enhanced_result)
        
        # 测试SuperEnhanced
        print("  运行 SuperEnhancedPipelinedWOA...")
        super_result = SuperEnhancedPipelinedWOA.run(scenario['n'], scenario['k'], network_config)
        super_results.append(super_result)
        
        # 对比结果
        enhanced_delay = enhanced_result['transmission_delay']
        super_delay = super_result['transmission_delay']
        improvement = ((enhanced_delay - super_delay) / enhanced_delay) * 100
        
        status = "✅" if improvement > 0 else "❌"
        print(f"  Enhanced时延: {enhanced_delay:.4f}")
        print(f"  SuperEnhanced时延: {super_delay:.4f}")
        print(f"  改进: {improvement:+.1f}% {status}")
    
    return enhanced_results, super_results, test_scenarios

def analyze_final_results(enhanced_results, super_results, test_scenarios):
    """分析最终结果"""
    print("\n" + "=" * 80)
    print("最终结果分析")
    print("=" * 80)
    
    # 计算平均性能
    enhanced_delays = [r['transmission_delay'] for r in enhanced_results]
    super_delays = [r['transmission_delay'] for r in super_results]
    
    enhanced_flows = [r['flow_consumption'] for r in enhanced_results]
    super_flows = [r['flow_consumption'] for r in super_results]
    
    enhanced_stds = [r['std_deviation'] for r in enhanced_results]
    super_stds = [r['std_deviation'] for r in super_results]
    
    # 计算平均改进
    delay_improvements = [((e - s) / e) * 100 for e, s in zip(enhanced_delays, super_delays)]
    flow_improvements = [((e - s) / e) * 100 for e, s in zip(enhanced_flows, super_flows)]
    std_improvements = [((e - s) / e) * 100 for e, s in zip(enhanced_stds, super_stds)]
    
    avg_delay_improvement = np.mean(delay_improvements)
    avg_flow_improvement = np.mean(flow_improvements)
    avg_std_improvement = np.mean(std_improvements)
    
    print("1. 平均性能对比:")
    print(f"  Enhanced平均时延: {np.mean(enhanced_delays):.4f}")
    print(f"  SuperEnhanced平均时延: {np.mean(super_delays):.4f}")
    print(f"  平均时延改进: {avg_delay_improvement:+.1f}%")
    
    print(f"\n  Enhanced平均流量: {np.mean(enhanced_flows):.4f}")
    print(f"  SuperEnhanced平均流量: {np.mean(super_flows):.4f}")
    print(f"  平均流量改进: {avg_flow_improvement:+.1f}%")
    
    print(f"\n  Enhanced平均负载均衡: {np.mean(enhanced_stds):.4f}")
    print(f"  SuperEnhanced平均负载均衡: {np.mean(super_stds):.4f}")
    print(f"  平均负载均衡改进: {avg_std_improvement:+.1f}%")
    
    # 计算胜率
    delay_wins = sum(1 for imp in delay_improvements if imp > 0)
    flow_wins = sum(1 for imp in flow_improvements if imp > 0)
    std_wins = sum(1 for imp in std_improvements if imp > 0)
    
    total_tests = len(test_scenarios)
    
    print(f"\n2. 胜率分析:")
    print(f"  时延胜率: {delay_wins}/{total_tests} ({delay_wins/total_tests*100:.1f}%)")
    print(f"  流量胜率: {flow_wins}/{total_tests} ({flow_wins/total_tests*100:.1f}%)")
    print(f"  负载均衡胜率: {std_wins}/{total_tests} ({std_wins/total_tests*100:.1f}%)")
    
    # 综合评估
    comprehensive_score = (avg_delay_improvement + avg_flow_improvement + avg_std_improvement) / 3
    
    print(f"\n3. 综合评估:")
    print(f"  综合改进得分: {comprehensive_score:+.1f}%")
    
    if comprehensive_score > 15:
        print("  🎉 SuperEnhanced算法显著超越Enhanced算法！")
        success = True
    elif comprehensive_score > 5:
        print("  ✅ SuperEnhanced算法超越Enhanced算法")
        success = True
    else:
        print("  ❌ SuperEnhanced算法仍需优化")
        success = False
    
    return success, comprehensive_score

def plot_final_verification(enhanced_results, super_results, test_scenarios):
    """绘制最终验证图"""
    print("\n生成最终验证图...")
    
    # 创建2x2子图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('最终验证：SuperEnhanced vs Enhanced', fontsize=16, fontweight='bold')
    
    scenarios = [s['name'] for s in test_scenarios]
    
    # 1. 传输时延对比
    ax1 = axes[0, 0]
    enhanced_delays = [r['transmission_delay'] for r in enhanced_results]
    super_delays = [r['transmission_delay'] for r in super_results]
    
    x = np.arange(len(scenarios))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, enhanced_delays, width, label='Enhanced', color='#e377c2', alpha=0.8)
    bars2 = ax1.bar(x + width/2, super_delays, width, label='SuperEnhanced', color='#ff1493', alpha=0.8)
    
    ax1.set_title('传输时延对比', fontsize=14)
    ax1.set_ylabel('传输时延')
    ax1.set_xticks(x)
    ax1.set_xticklabels(scenarios)
    ax1.legend()
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 2. 流量消耗对比
    ax2 = axes[0, 1]
    enhanced_flows = [r['flow_consumption'] for r in enhanced_results]
    super_flows = [r['flow_consumption'] for r in super_results]
    
    bars1 = ax2.bar(x - width/2, enhanced_flows, width, label='Enhanced', color='#e377c2', alpha=0.8)
    bars2 = ax2.bar(x + width/2, super_flows, width, label='SuperEnhanced', color='#ff1493', alpha=0.8)
    
    ax2.set_title('流量消耗对比', fontsize=14)
    ax2.set_ylabel('流量消耗')
    ax2.set_xticks(x)
    ax2.set_xticklabels(scenarios)
    ax2.legend()
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 3. 负载均衡对比
    ax3 = axes[1, 0]
    enhanced_stds = [r['std_deviation'] for r in enhanced_results]
    super_stds = [r['std_deviation'] for r in super_results]
    
    bars1 = ax3.bar(x - width/2, enhanced_stds, width, label='Enhanced', color='#e377c2', alpha=0.8)
    bars2 = ax3.bar(x + width/2, super_stds, width, label='SuperEnhanced', color='#ff1493', alpha=0.8)
    
    ax3.set_title('负载均衡对比', fontsize=14)
    ax3.set_ylabel('标准差')
    ax3.set_xticks(x)
    ax3.set_xticklabels(scenarios)
    ax3.legend()
    ax3.grid(True, alpha=0.3, axis='y')
    
    # 4. 改进百分比
    ax4 = axes[1, 1]
    
    delay_improvements = [((e - s) / e) * 100 for e, s in zip(enhanced_delays, super_delays)]
    flow_improvements = [((e - s) / e) * 100 for e, s in zip(enhanced_flows, super_flows)]
    std_improvements = [((e - s) / e) * 100 for e, s in zip(enhanced_stds, super_stds)]
    
    metrics = ['时延', '流量', '负载均衡']
    avg_improvements = [
        np.mean(delay_improvements),
        np.mean(flow_improvements),
        np.mean(std_improvements)
    ]
    
    colors = ['#ff1493' if imp > 0 else '#ff6b6b' for imp in avg_improvements]
    bars = ax4.bar(metrics, avg_improvements, color=colors, alpha=0.8)
    
    # 添加数值标签
    for bar, imp in zip(bars, avg_improvements):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height,
               f'{imp:+.1f}%', ha='center', va='bottom' if imp > 0 else 'top', 
               fontsize=12, fontweight='bold')
    
    ax4.set_title('平均改进百分比', fontsize=14)
    ax4.set_ylabel('改进百分比 (%)')
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax4.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('results/final_verification.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 最终验证图已保存: results/final_verification.png")

def main():
    """主函数"""
    if not os.path.exists('results'):
        os.makedirs('results')
    
    try:
        # 运行最终验证测试
        enhanced_results, super_results, test_scenarios = final_verification_test()
        
        # 分析结果
        success, score = analyze_final_results(enhanced_results, super_results, test_scenarios)
        
        # 绘制验证图
        plot_final_verification(enhanced_results, super_results, test_scenarios)
        
        print(f"\n🎉 最终验证完成！")
        
        if success:
            print(f"✅ SuperEnhanced算法成功超越Enhanced算法！")
            print(f"📊 综合改进得分: {score:+.1f}%")
            print(f"🎊 您的论文现在有了强有力的第二个创新点支撑！")
        else:
            print(f"⚠️ SuperEnhanced算法仍需进一步优化")
            print(f"📊 当前得分: {score:+.1f}%")
        
    except Exception as e:
        print(f"验证出错: {str(e)}")
        raise

if __name__ == "__main__":
    main()
