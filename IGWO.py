import numpy as np
import time
from standardTopo import bandWidth, blockSize, ny_graph, topo_list, num_nodes
import copy
import networkx as nx
import random
import networkx.algorithms.approximation as nx_approx
import statistics
from deap import base, creator, tools
from scipy import stats
from scipy.stats import sem
import math

class ImprovedGreyWolfOptimizer:
    def __init__(self, nwolves, max_iter):
        random.seed(3)
        self.nwolves = nwolves  # 种群规模
        self.max_iter = max_iter  # 最大迭代次数
        self.best_wolf = None  # 初始化最优解
        self.best_fitness = (float('inf'), float('inf'), float('inf'))  # 初始化最优适应度

        # 初始化其他属性
        self.totalproviders = None
        self.k = None
        self.targets = None
        self.wolves = []
        
        # IGWO特有参数
        self.a_init = 2.0  # 初始搜索步长
        self.a_final = 0.0  # 最终搜索步长
        
        # 清理之前可能存在的DEAP类
        if 'FitnessMin' in creator.__dict__:
            del creator.FitnessMin
        if 'Individual' in creator.__dict__:
            del creator.Individual
            
        self.create_deap_classes()

    def create_deap_classes(self):
        creator.create("FitnessMin", base.Fitness, weights=(-1.0, -1.0, -1.0))
        creator.create("Individual", list, fitness=creator.FitnessMin, wa=None)

    def evaluate(self, individual):
        # 添加路径验证
        if not self.validate_paths(individual.wa):
            return (float('inf'), float('inf'), float('inf'))
        
        # 添加连通性验证
        if not self.verify_connectivity(individual.wa):
            return (float('inf'), float('inf'), float('inf'))
        
        try:
            print("开始评估个体...")
            G_tree = copy.deepcopy(ny_graph)
            edge_loads = {}  # 记录每条边的负载情况
            total_delay = 0
            total_flow = 0
            valid_paths = 0
            
            # 全局流量预算
            global_flow_budget = 40  # 进一步降低全局流量预算
            
            # 路径共享机制
            shared_paths = {}  # 记录已使用的路径
            flags = {}  # 记录边的使用情况
            
            for r in range(len(individual.wa)):
                print(f"评估树 {r + 1}/{len(individual.wa)}")
                tree_delay = 0
                tree_paths = 0
                
                if not individual.wa[r]:
                    continue
                    
                for path in individual.wa[r]:
                    if not path or len(path) < 2:
                        continue
                        
                    path_delay = 0
                    path_valid = True
                    
                    # 检查是否可以复用现有路径
                    path_key = (path[0], path[-1])
                    if path_key in shared_paths:
                        existing_path = shared_paths[path_key]
                        if len(existing_path) <= len(path):
                            path = existing_path
                    
                    # 计算路径的延迟和流量
                    for i in range(len(path) - 1):
                        source = path[i]
                        destination = path[i + 1]
                        
                        if not (0 <= source < num_nodes and 0 <= destination < num_nodes):
                            path_valid = False
                            break
                            
                        if not G_tree.has_edge(source, destination):
                            path_valid = False
                            break
                            
                        current_bw = G_tree[source][destination].get('bw', 0)
                        if current_bw < blockSize:
                            path_valid = False
                            break
                            
                        # 检查是否已经使用过这条边
                        if (r, source, destination) not in flags and (r, destination, source) not in flags:
                            total_flow += 1  # 只在第一次使用边时计数
                            if total_flow > global_flow_budget:
                                path_valid = False
                                total_flow -= 1  # 回退计数
                                break
                            flags[(r, source, destination)] = flags[(r, destination, source)] = 1
                            
                            # 更新带宽和负载
                            G_tree[source][destination]['bw'] -= blockSize
                            edge_key = tuple(sorted([source, destination]))
                            edge_loads[edge_key] = edge_loads.get(edge_key, 0) + blockSize
                            
                        # 计算延迟，考虑路径长度惩罚
                        path_length_penalty = 3.0 if len(path) > 2 else 1.0  # 加大对非直接路径的惩罚
                        path_delay += (blockSize / current_bw) * path_length_penalty
                    
                    if path_valid:
                        tree_delay += path_delay
                        tree_paths += 1
                        # 记录有效路径以供共享
                        shared_paths[path_key] = path
                
                if tree_paths > 0:
                    total_delay += tree_delay
                    valid_paths += tree_paths
            
            # 计算最终指标
            if valid_paths == 0:
                print("警告: 没有有效路径")
                return (float('inf'), float('inf'), float('inf'))
            
            average_delay = total_delay / valid_paths
            
            # 计算负载均衡
            load_ratios = []
            max_load_ratio = 0
            for edge, load in edge_loads.items():
                s, d = edge
                total_bw = bandWidth[s][d]
                if total_bw > 0:
                    load_ratio = load / total_bw
                    load_ratios.append(load_ratio)
                    max_load_ratio = max(max_load_ratio, load_ratio)
            
            load_balance = statistics.pstdev(load_ratios) if len(load_ratios) > 1 else 1.0
            
            # 增加流量惩罚
            flow_penalty = max(1.0, (total_flow / 40.0) ** 4)  # 更激进的流量惩罚
            
            # 调整指标权重
            weighted_delay = average_delay * (1 + 0.1 * len(load_ratios))  # 考虑使用的边数
            weighted_flow = total_flow * flow_penalty
            weighted_balance = load_balance * (1 + max_load_ratio)  # 考虑最大负载
            
            print(f"评估完成: 延迟={weighted_delay:.4f}, 流量={weighted_flow:.4f}, 负载均衡={weighted_balance:.4f}")
            return (weighted_delay, weighted_flow, weighted_balance)
            
        except Exception as e:
            print(f"评估过程中发生错误: {e}")
            return (float('inf'), float('inf'), float('inf'))

    def sub_evaluate_latency(self, paths, TreeweightMatrix):
        try:
            print("计算延迟...")
            max_latency = 0
            repeated_nodes = set()
            node_latencies = {}
            
            # First pass: identify repeated nodes and their base latencies
            for path_idx, path in enumerate(paths):
                try:
                    if not path or len(path) < 2:
                        print(f"警告: 路径 {path_idx} 无效")
                        continue
                        
                    path_nodes = {}
                    for i in range(len(path) - 1):
                        try:
                            source = path[i]
                            destination = path[i + 1]
                            
                            if not (0 <= source < num_nodes and 0 <= destination < num_nodes):
                                print(f"警告: 无效的节点索引 ({source}, {destination})")
                                continue
                            
                            # Track node occurrences
                            path_nodes[source] = path_nodes.get(source, 0) + 1
                            if path_nodes[source] > 1:
                                repeated_nodes.add(source)
                            
                            # Calculate base latency
                            if source not in node_latencies:
                                node_latencies[source] = 0
                                
                            try:
                                weight = TreeweightMatrix[source][destination]
                                if weight > 0 and bandWidth[source][destination] > 0:
                                    node_latencies[source] = max(
                                        node_latencies[source],
                                        weight * blockSize / bandWidth[source][destination]
                                    )
                            except (IndexError, KeyError, ZeroDivisionError) as e:
                                print(f"计算基础延迟时出错: {e}")
                                continue
                                
                        except Exception as e:
                            print(f"处理节点对时出错: {e}")
                            continue
                            
                except Exception as e:
                    print(f"处理路径 {path_idx} 时出错: {e}")
                    continue
            
            # Second pass: calculate actual path latencies
            for path_idx, path in enumerate(paths):
                try:
                    if not path or len(path) < 2:
                        continue
                        
                    path_latency = 0
                    for i in range(len(path) - 1):
                        try:
                            source = path[i]
                            destination = path[i + 1]
                            
                            if not (0 <= source < num_nodes and 0 <= destination < num_nodes):
                                continue
                            
                            try:
                                weight = TreeweightMatrix[source][destination]
                                
                                if source in repeated_nodes:
                                    # For repeated nodes, use the maximum latency up to this point
                                    path_latency = max(path_latency, node_latencies.get(source, 0))
                                
                                # Add current edge latency
                                if weight > 0 and bandWidth[source][destination] > 0:
                                    path_latency += weight * blockSize / bandWidth[source][destination]
                                    
                            except (IndexError, KeyError, ZeroDivisionError) as e:
                                print(f"计算路径延迟时出错: {e}")
                                continue
                                
                        except Exception as e:
                            print(f"处理边时出错: {e}")
                            continue
                    
                    max_latency = max(max_latency, path_latency)
                    
                except Exception as e:
                    print(f"计算路径 {path_idx} 的延迟时出错: {e}")
                    continue
            
            print("延迟计算完成")
            return max_latency, len(repeated_nodes)
            
        except Exception as e:
            print(f"延迟评估过程中发生严重错误: {e}")
            return float('inf'), 0

    def calcpsdv(self, G, topo_list):
        weight_list = [100 * (bandWidth[edge[0]][edge[1]] - G[edge[0]][edge[1]]['bw']) / bandWidth[edge[0]][edge[1]] 
                      for edge in topo_list]
        return statistics.pstdev(weight_list)

    def create_individual(self):
        try:
            print("开始创建个体...")
            individual = creator.Individual()
            individual.wa = [[] for _ in range(len(self.targets))]
            
            # 全局资源跟踪
            G_temp = copy.deepcopy(ny_graph)
            edge_count = 0  # 记录使用的边数
            max_edges_per_tree = 8  # 每棵树最多使用的边数
            
            # 路径共享机制
            shared_paths = {}
            flags = {}  # 记录边的使用情况
            
            for index, target in enumerate(self.targets):
                print(f"处理目标 {index + 1}/{len(self.targets)}")
                
                if not target:
                    print(f"警告: 目标 {index} 为空")
                    continue
                    
                start_time = time.time()
                timeout = 5  # 5秒超时
                
                try:
                    target_node = random.choice(list(target))
                    available_providers = list(set(self.totalproviders[index]) - {target_node})
                    
                    if not available_providers:
                        print(f"警告: 目标 {index} 没有可用的providers")
                        continue
                        
                    onetree_wa = [[] for _ in range(self.k)]
                    tree_edges = 0  # 当前树使用的边数
                    
                    # 优先选择直接连接的provider
                    direct_providers = []
                    for provider in available_providers:
                        if G_temp.has_edge(provider, target_node):
                            if G_temp[provider][target_node].get('bw', 0) >= blockSize:
                                direct_providers.append(provider)
                    
                    # 如果直接连接不够，尝试找到最短路径
                    if len(direct_providers) < self.k:
                        # 计算所有provider到目标的距离
                        provider_distances = {}
                        for provider in available_providers:
                            if provider not in direct_providers:
                                try:
                                    path = nx.shortest_path(G_temp, provider, target_node)
                                    if len(path) <= 3:  # 只考虑最多有一个中间节点的路径
                                        # 验证路径的带宽
                                        valid = True
                                        path_edges = 0
                                        for i in range(len(path) - 1):
                                            s, d = path[i], path[i + 1]
                                            if G_temp[s][d].get('bw', 0) < blockSize:
                                                valid = False
                                                break
                                            if (index, s, d) not in flags and (index, d, s) not in flags:
                                                path_edges += 1
                                        if valid:
                                            provider_distances[provider] = (path, path_edges)
                                except:
                                    continue
                        
                        # 按路径所需的新边数排序
                        sorted_providers = sorted(provider_distances.items(), key=lambda x: x[1][1])
                        for provider, (path, _) in sorted_providers:
                            if len(direct_providers) >= self.k:
                                break
                            direct_providers.append(provider)
                    
                    # 分配provider
                    assigned_count = 0
                    
                    # 随机打乱provider顺序以增加多样性
                    random.shuffle(direct_providers)
                    
                    for provider in direct_providers:
                        if assigned_count >= self.k or tree_edges >= max_edges_per_tree:
                            break
                        if edge_count >= 40:  # 全局边数限制
                            break
                            
                        # 检查是否可以复用现有路径
                        path_key = (provider, target_node)
                        if path_key in shared_paths:
                            path = shared_paths[path_key]
                        else:
                            # 获取到目标的路径
                            try:
                                path = nx.shortest_path(G_temp, provider, target_node)
                                if len(path) > 3:  # 最多允许一个中间节点
                                    continue
                                    
                                # 计算新边数
                                new_edges = 0
                                for i in range(len(path) - 1):
                                    s, d = path[i], path[i + 1]
                                    if (index, s, d) not in flags and (index, d, s) not in flags:
                                        new_edges += 1
                                
                                # 检查边数限制
                                if tree_edges + new_edges > max_edges_per_tree or edge_count + new_edges > 40:
                                    continue
                                    
                                # 验证带宽
                                valid = True
                                for i in range(len(path) - 1):
                                    s, d = path[i], path[i + 1]
                                    if G_temp[s][d].get('bw', 0) < blockSize:
                                        valid = False
                                        break
                                
                                if not valid:
                                    continue
                                    
                                shared_paths[path_key] = path
                                
                            except Exception:
                                continue
                        
                        # 更新资源使用
                        for i in range(len(path) - 1):
                            s, d = path[i], path[i + 1]
                            if (index, s, d) not in flags and (index, d, s) not in flags:
                                G_temp[s][d]['bw'] -= blockSize
                                flags[(index, s, d)] = flags[(index, d, s)] = 1
                                tree_edges += 1
                                edge_count += 1
                        onetree_wa[assigned_count] = path
                        assigned_count += 1
                    
                    # 填充剩余位置，优先使用直接连接
                    remaining_providers = [p for p in available_providers if p not in [path[0] for path in onetree_wa if path]]
                    for i in range(assigned_count, self.k):
                        if remaining_providers:
                            provider = remaining_providers.pop(0)
                            if G_temp.has_edge(provider, target_node):
                                onetree_wa[i] = [provider, target_node]
                            else:
                                onetree_wa[i] = [provider, target_node]  # 仍然使用直接连接
                        else:
                            onetree_wa[i] = [0, target_node]
                    
                except Exception as e:
                    print(f"处理目标 {index} 时出错: {e}")
                    for i in range(self.k):
                        if i < len(available_providers):
                            onetree_wa[i] = [available_providers[i], target_node]
                        else:
                            onetree_wa[i] = [0, target_node]
                
                individual.wa[index] = onetree_wa
            
            try:
                print("评估个体适应度...")
                individual.fitness.values = self.evaluate(individual)
                if sum(individual.fitness.values) < sum(self.best_fitness):
                    self.best_fitness = individual.fitness.values
                    self.best_wolf = copy.deepcopy(individual.wa)
            except Exception as e:
                print(f"评估适应度时出错: {e}")
                individual.fitness.values = (float('inf'), float('inf'), float('inf'))
            
            print("个体创建完成")
            return individual
            
        except Exception as e:
            print(f"创建个体时发生严重错误: {e}")
            individual = creator.Individual()
            individual.wa = [[[0, 1]] * self.k for _ in range(len(self.targets))]
            individual.fitness.values = (float('inf'), float('inf'), float('inf'))
            return individual

    def compress_path(self, path, G):
        """尝试压缩路径，移除不必要的中间节点"""
        if len(path) <= 2:
            return path
            
        compressed = [path[0]]
        i = 0
        while i < len(path) - 1:
            current = path[i]
            # 尝试跳过中间节点
            for j in range(min(len(path) - 1, i + 3), i, -1):
                if G.has_edge(current, path[j]):
                    compressed.append(path[j])
                    i = j
                    break
            else:
                compressed.append(path[i + 1])
                i += 1
        
        return compressed

    def chaos_mapping(self, x):
        # 使用Logistic映射进行混沌初始化
        return 4 * x * (1 - x)

    def adaptive_weight(self, iter):
        # 自适应权重计算
        return self.a_init - (self.a_init - self.a_final) * (iter / self.max_iter)

    def update_position(self, current_wolf, alpha, beta, delta, iter):
        try:
            print(f"更新位置，迭代 {iter}")
            start_time = time.time()
            timeout = 3  # 3秒超时
            
            a = self.adaptive_weight(iter)
            new_wa = copy.deepcopy(current_wolf.wa)
            G_temp = copy.deepcopy(ny_graph)
            
            # 全局资源跟踪
            edge_loads = {}
            edge_count = 0
            used_paths = {}
            flags = {}  # 记录边的使用情况
            
            # 首先统计当前路径的使用情况
            for r in range(len(new_wa)):
                for path in new_wa[r]:
                    if not path or len(path) < 2:
                        continue
                        
                    for i in range(len(path) - 1):
                        s, d = path[i], path[i+1]
                        if (r, s, d) not in flags and (r, d, s) not in flags:
                            edge_count += 1
                            flags[(r, s, d)] = flags[(r, d, s)] = 1
                            edge = tuple(sorted([s, d]))
                            edge_loads[edge] = edge_loads.get(edge, 0) + blockSize
                            used_paths[(path[0], path[-1])] = path
            
            # 更新边的权重
            for u, v, data in G_temp.edges(data=True):
                try:
                    edge = tuple(sorted([u, v]))
                    current_load = edge_loads.get(edge, 0)
                    total_bw = bandWidth[u][v]
                    used_ratio = current_load / total_bw if total_bw > 0 else 1
                    
                    # 根据负载情况设置权重
                    if used_ratio > 0.8:
                        weight = float('inf')  # 禁止使用过载的边
                    elif used_ratio > 0.6:
                        weight = data.get('true_weight', 1.0) * 10  # 增加高负载边的权重
                    elif used_ratio > 0.4:
                        weight = data.get('true_weight', 1.0) * 3
                    else:
                        weight = data.get('true_weight', 1.0)
                    
                    G_temp[u][v]['weight'] = weight
                    
                except Exception:
                    G_temp.remove_edge(u, v)
            
            for r in range(len(new_wa)):
                print(f"处理树 {r + 1}/{len(new_wa)}")
                tree_edges = 0
                
                for i in range(len(new_wa[r])):
                    if time.time() - start_time > timeout:
                        return current_wolf
                        
                    try:
                        path = new_wa[r][i]
                        if not path or len(path) < 2:
                            continue
                            
                        start_node = path[0]
                        end_node = path[-1]
                        
                        if not (G_temp.has_node(start_node) and G_temp.has_node(end_node)):
                            continue
                        
                        # 如果是直接连接且负载不高，保持不变
                        if len(path) == 2:
                            edge = tuple(sorted([start_node, end_node]))
                            if edge_loads.get(edge, 0) / bandWidth[start_node][end_node] < 0.4:
                                continue
                        
                        # 尝试复用现有路径
                        path_key = (start_node, end_node)
                        if path_key in used_paths and used_paths[path_key] != path:
                            existing_path = used_paths[path_key]
                            if len(existing_path) <= len(path):
                                # 验证路径是否可用
                                valid = True
                                for j in range(len(existing_path) - 1):
                                    s, d = existing_path[j], existing_path[j+1]
                                    if G_temp[s][d].get('bw', 0) < blockSize:
                                        valid = False
                                        break
                                if valid:
                                    new_wa[r][i] = existing_path
                                    continue
                        
                        try:
                            # 尝试找到更好的路径
                            new_path = nx.shortest_path(G_temp, start_node, end_node, weight='weight')
                            
                            # 验证路径长度和流量限制
                            if len(new_path) > 3 or len(new_path) >= len(path):
                                continue
                                
                            # 计算新边数
                            new_edges = 0
                            for j in range(len(new_path) - 1):
                                s, d = new_path[j], new_path[j+1]
                                if (r, s, d) not in flags and (r, d, s) not in flags:
                                    new_edges += 1
                            
                            # 检查边数限制
                            if edge_count - len(path) + 1 + new_edges > 40:  # 更严格的边数限制
                                continue
                                
                            # 验证路径
                            valid = True
                            for j in range(len(new_path) - 1):
                                s, d = new_path[j], new_path[j+1]
                                if G_temp[s][d].get('bw', 0) < blockSize:
                                    valid = False
                                    break
                            
                            if valid:
                                # 更新资源使用
                                for j in range(len(path) - 1):
                                    s, d = path[j], path[j+1]
                                    if (r, s, d) in flags:
                                        edge_count -= 1
                                        del flags[(r, s, d)]
                                        del flags[(r, d, s)]
                                        edge = tuple(sorted([s, d]))
                                        edge_loads[edge] -= blockSize
                                    
                                for j in range(len(new_path) - 1):
                                    s, d = new_path[j], new_path[j+1]
                                    if (r, s, d) not in flags and (r, d, s) not in flags:
                                        edge_count += 1
                                        flags[(r, s, d)] = flags[(r, d, s)] = 1
                                        edge = tuple(sorted([s, d]))
                                        edge_loads[edge] = edge_loads.get(edge, 0) + blockSize
                                        G_temp[s][d]['bw'] -= blockSize
                                
                                new_wa[r][i] = new_path
                                used_paths[(start_node, end_node)] = new_path
                            
                        except Exception:
                            continue
                            
                    except Exception:
                        continue
            
            try:
                if time.time() - start_time > timeout:
                    return current_wolf
                    
                new_wolf = creator.Individual()
                new_wolf.wa = new_wa
                new_wolf.fitness.values = self.evaluate(new_wolf)
                
                # 严格的接受条件
                if (new_wolf.fitness.values[1] < current_wolf.fitness.values[1] * 0.85 and  # 流量必须显著减少
                    new_wolf.fitness.values[2] <= current_wolf.fitness.values[2] * 1.1):  # 负载均衡可以稍微牺牲
                    print("位置更新完成")
                    return new_wolf
                return current_wolf
                
            except Exception as e:
                print(f"创建新狼时出错: {e}")
                return current_wolf
                
        except Exception as e:
            print(f"更新位置时发生严重错误: {e}")
            return current_wolf

    def optimize(self, providers, q, targets):
        """Optimizes the network using IGWO algorithm"""
        # Initialize best tracking variables
        overall_best_fitness = (float('inf'), float('inf'), float('inf'))
        overall_best_wolf = None
        
        # Run multiple times to get statistical results
        for run in range(30):  # 运行30次
            print(f"\nRun {run + 1}/30")
            result = self.single_run(providers, q, targets)
            
            # Update best if this run produced better results
            if sum(result) < sum(overall_best_fitness):
                overall_best_fitness = result
                overall_best_wolf = copy.deepcopy(self.best_wolf)
        
        return overall_best_wolf, overall_best_fitness

    def is_similar(self, wolf1, wolf2, threshold=0.9):
        """Helper method to check if two wolves are too similar"""
        if len(wolf1.wa) != len(wolf2.wa):
            return False
        
        similar_paths = 0
        total_paths = 0
        
        for tree1, tree2 in zip(wolf1.wa, wolf2.wa):
            for path1, path2 in zip(tree1, tree2):
                total_paths += 1
                common_nodes = set(path1) & set(path2)
                similarity = len(common_nodes) / max(len(path1), len(path2))
                if similarity > threshold:
                    similar_paths += 1
        
        return similar_paths / total_paths > threshold if total_paths > 0 else False 

    def validate_paths(self, wa):
        """验证所有路径的完整性和有效性"""
        for tree in wa:
            for path in tree:
                if not self.is_valid_path(path):
                    return False
        return True

    def verify_connectivity(self, wa):
        """验证是否所有必要的连接都存在"""
        for tree in wa:
            for path in tree:
                if not nx.has_path(ny_graph, path[0], path[-1]):
                    return False
        return True 

    def single_run(self, providers, q, targets):
        """Performs a single run of the IGWO algorithm"""
        # Initialize parameters
        self.totalproviders = providers
        self.k = q
        self.targets = targets
        
        # Initialize population
        self.wolves = [self.create_individual() for _ in range(self.nwolves)]
        
        # Sort wolves by fitness
        self.wolves.sort(key=lambda x: sum(x.fitness.values))
        alpha = copy.deepcopy(self.wolves[0])
        beta = copy.deepcopy(self.wolves[1])
        delta = copy.deepcopy(self.wolves[2])
        
        # Main optimization loop
        for iter in range(self.max_iter):
            print(f"Iteration {iter + 1}/{self.max_iter}")
            
            # Update each wolf's position
            for i in range(len(self.wolves)):
                self.wolves[i] = self.update_position(self.wolves[i], alpha, beta, delta, iter)
            
            # Sort wolves and update alpha, beta, delta
            self.wolves.sort(key=lambda x: sum(x.fitness.values))
            
            # Update leaders if better solutions are found
            if sum(self.wolves[0].fitness.values) < sum(alpha.fitness.values):
                alpha = copy.deepcopy(self.wolves[0])
            if sum(self.wolves[1].fitness.values) < sum(beta.fitness.values):
                beta = copy.deepcopy(self.wolves[1])
            if sum(self.wolves[2].fitness.values) < sum(delta.fitness.values):
                delta = copy.deepcopy(self.wolves[2])
            
            # Update best solution if needed
            if sum(alpha.fitness.values) < sum(self.best_fitness):
                self.best_fitness = alpha.fitness.values
                self.best_wolf = copy.deepcopy(alpha.wa)
                
            print(f"Best fitness at iteration {iter + 1}: {self.best_fitness}")
        
        return self.best_fitness 

    def is_valid_path(self, path):
        """Validates a single path"""
        if not path or len(path) < 2:
            return False
        
        # Check if all nodes are valid
        for node in path:
            if not (0 <= node < num_nodes):
                return False
            
        # Check if all edges exist and have sufficient bandwidth
        for i in range(len(path) - 1):
            source = path[i]
            destination = path[i + 1]
            
            if not ny_graph.has_edge(source, destination):
                return False
            
            if ny_graph[source][destination].get('bw', 0) < blockSize:
                return False
            
        return True