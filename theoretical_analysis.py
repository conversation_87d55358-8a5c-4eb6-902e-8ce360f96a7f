import numpy as np
import matplotlib.pyplot as plt
from math import log, sqrt
import networkx as nx

def complexity_analysis(n, k):
    """
    算法复杂度分析
    - WOA算法：O(iter_max * num_whales * n * k)
    - PipelinedWOA：O(iter_max * num_whales * n * k / num_stages)
    - EnhancedPipelinedWOA：O(iter_max * num_whales * n * k / num_stages + merging_cost)
    """
    # 基本参数
    iter_max = 100
    num_whales = 45
    num_stages = 4
    merging_cost = n * log(n)  # 路径合并的额外开销
    
    # 计算各算法的理论时间复杂度
    woa_complexity = iter_max * num_whales * n * k
    pwoa_complexity = iter_max * num_whales * n * k / num_stages
    epwoa_complexity = iter_max * num_whales * n * k / num_stages + merging_cost
    
    return {
        'WOA': woa_complexity,
        'PipelinedWOA': pwoa_complexity,
        'EnhancedPipelinedWOA': epwoa_complexity
    }

def convergence_analysis(solution_history):
    """
    收敛性分析
    基于Lyapunov稳定性理论分析算法的收敛性
    """
    iterations = len(solution_history)
    
    # 计算收敛速率
    convergence_rates = []
    for i in range(1, iterations):
        rate = abs(solution_history[i] - solution_history[i-1]) / abs(solution_history[i-1])
        convergence_rates.append(rate)
    
    # 判断是否满足收敛条件
    is_converged = all(rate < 0.01 for rate in convergence_rates[-5:])  # 连续5次变化率小于1%
    
    # 计算Lyapunov函数值
    lyapunov_values = []
    optimal_value = min(solution_history)
    for value in solution_history:
        lyapunov = (value - optimal_value) ** 2
        lyapunov_values.append(lyapunov)
    
    return {
        'convergence_rates': convergence_rates,
        'is_converged': is_converged,
        'lyapunov_values': lyapunov_values
    }

def optimality_bounds(G, k):
    """
    最优解边界分析
    使用网络特征和理论界限分析算法解的质量边界
    """
    n = G.number_of_nodes()
    
    # 计算理论下界
    # 1. 最短路径长度下界
    min_path_length = nx.average_shortest_path_length(G)
    lower_bound_delay = min_path_length * k
    
    # 2. 最小带宽消耗下界
    min_edges = k - 1
    lower_bound_flow = min_edges
    
    # 计算理论上界
    # 1. 最大路径长度上界
    diameter = nx.diameter(G)
    upper_bound_delay = diameter * k
    
    # 2. 最大带宽消耗上界
    max_edges = min(k * (k-1) // 2, G.number_of_edges())
    upper_bound_flow = max_edges
    
    return {
        'delay_bounds': (lower_bound_delay, upper_bound_delay),
        'flow_bounds': (lower_bound_flow, upper_bound_flow)
    }

def plot_convergence_analysis(convergence_results):
    """绘制收敛性分析结果"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 收敛率曲线
    iterations = range(1, len(convergence_results['convergence_rates'])+1)
    ax1.plot(iterations, convergence_results['convergence_rates'], 'b-')
    ax1.set_xlabel('迭代次数')
    ax1.set_ylabel('收敛率')
    ax1.set_title('算法收敛率变化')
    ax1.grid(True)
    
    # Lyapunov函数值曲线
    iterations = range(len(convergence_results['lyapunov_values']))
    ax2.plot(iterations, convergence_results['lyapunov_values'], 'r-')
    ax2.set_xlabel('迭代次数')
    ax2.set_ylabel('Lyapunov函数值')
    ax2.set_title('Lyapunov稳定性分析')
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig('results/convergence_analysis.png')
    plt.close()

def complexity_comparison_plot(n_values, k_values):
    """绘制不同规模下的复杂度对比"""
    complexities = []
    for n, k in zip(n_values, k_values):
        result = complexity_analysis(n, k)
        complexities.append(result)
    
    plt.figure(figsize=(10, 6))
    for algo in ['WOA', 'PipelinedWOA', 'EnhancedPipelinedWOA']:
        values = [c[algo] for c in complexities]
        plt.plot(n_values, values, '-o', label=algo)
    
    plt.xlabel('节点数量(n)')
    plt.ylabel('理论时间复杂度')
    plt.title('算法复杂度对比')
    plt.legend()
    plt.grid(True)
    plt.yscale('log')  # 使用对数刻度更好地展示复杂度差异
    
    plt.tight_layout()
    plt.savefig('results/complexity_comparison.png')
    plt.close()

def bounds_analysis_plot(G, k_values):
    """绘制不同k值下的最优解边界分析"""
    bounds_results = []
    for k in k_values:
        bounds = optimality_bounds(G, k)
        bounds_results.append(bounds)
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 时延边界
    delay_lower = [b['delay_bounds'][0] for b in bounds_results]
    delay_upper = [b['delay_bounds'][1] for b in bounds_results]
    ax1.fill_between(k_values, delay_lower, delay_upper, alpha=0.3)
    ax1.plot(k_values, delay_lower, 'b--', label='下界')
    ax1.plot(k_values, delay_upper, 'r--', label='上界')
    ax1.set_xlabel('k值')
    ax1.set_ylabel('时延')
    ax1.set_title('时延边界分析')
    ax1.legend()
    ax1.grid(True)
    
    # 流量边界
    flow_lower = [b['flow_bounds'][0] for b in bounds_results]
    flow_upper = [b['flow_bounds'][1] for b in bounds_results]
    ax2.fill_between(k_values, flow_lower, flow_upper, alpha=0.3)
    ax2.plot(k_values, flow_lower, 'b--', label='下界')
    ax2.plot(k_values, flow_upper, 'r--', label='上界')
    ax2.set_xlabel('k值')
    ax2.set_ylabel('流量消耗')
    ax2.set_title('流量边界分析')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig('results/bounds_analysis.png')
    plt.close()