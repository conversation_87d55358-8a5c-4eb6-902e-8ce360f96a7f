#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本 - 带超时机制和详细调试信息
"""

import time
import sys
import threading
from standardTopo import NetworkConfiguration
from WOA import WhaleOptimizationAlgorithm
from PopelineWOA import PipelinedWOA
from EnhancedPipelinedWOA import EnhancedPipelinedWOA

class TimeoutError(Exception):
    pass

def run_with_timeout(func, timeout_seconds=300):
    """带超时机制运行函数 - 使用线程实现"""
    result = [None]
    exception = [None]

    def target():
        try:
            result[0] = func()
        except Exception as e:
            exception[0] = e

    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout_seconds)

    if thread.is_alive():
        print(f"算法运行超时 ({timeout_seconds}秒)")
        return None
    elif exception[0] is not None:
        print(f"算法运行出错: {str(exception[0])}")
        return None
    else:
        return result[0]

def test_single_algorithm(algo_name, n, k, network_config):
    """测试单个算法"""
    print(f"\n开始测试 {algo_name}...")
    print(f"参数: n={n}, k={k}")

    start_time = time.time()

    def run_algo():
        if algo_name == 'WOA算法':
            print("  调用 WhaleOptimizationAlgorithm.run...")
            return WhaleOptimizationAlgorithm.run(n, k, network_config)
        elif algo_name == 'PipelinedWOA算法':
            print("  调用 PipelinedWOA.run...")
            return PipelinedWOA.run(n, k, network_config)
        elif algo_name == 'EnhancedPipelinedWOA算法':
            print("  调用 EnhancedPipelinedWOA.run...")
            return EnhancedPipelinedWOA.run(n, k, network_config)
        else:
            raise ValueError(f"未知算法: {algo_name}")

    # 运行算法，设置5分钟超时
    result = run_with_timeout(run_algo, timeout_seconds=300)

    end_time = time.time()
    runtime = end_time - start_time

    if result is not None:
        print(f"  ✓ 算法运行成功")
        print(f"  运行时间: {runtime:.2f}秒")
        print(f"  时延: {result['transmission_delay']:.4f}")
        print(f"  流量: {result['flow_consumption']:.4f}")
        print(f"  负载均衡: {result['std_deviation']:.4f}")
        return {
            'success': True,
            'runtime': runtime,
            'result': result
        }
    else:
        print(f"  ✗ 算法运行失败")
        print(f"  运行时间: {runtime:.2f}秒")
        return {
            'success': False,
            'runtime': runtime,
            'result': None
        }

def simple_test():
    """简单测试 - 逐个测试算法"""
    print("=" * 60)
    print("简单测试脚本 - 带超时机制")
    print("=" * 60)

    # 使用较小的参数进行测试
    n, k = 30, 100  # 减小参数以加快测试
    network_config = NetworkConfiguration()

    print(f"网络配置信息:")
    print(f"  节点数: {len(network_config.nodes)}")
    ny_graph = network_config.get_network_graph()
    print(f"  边数: {len(ny_graph.edges())}")

    algorithms = ['WOA算法', 'PipelinedWOA算法', 'EnhancedPipelinedWOA算法']
    results = {}

    total_start_time = time.time()

    for algo_name in algorithms:
        try:
            result = test_single_algorithm(algo_name, n, k, network_config)
            results[algo_name] = result

            # 每个算法测试完后暂停一下
            print("  等待2秒后继续...")
            time.sleep(2)

        except KeyboardInterrupt:
            print(f"\n用户中断了 {algo_name} 的测试")
            break
        except Exception as e:
            print(f"\n测试 {algo_name} 时发生未预期错误: {str(e)}")
            results[algo_name] = {
                'success': False,
                'runtime': 0,
                'result': None
            }

    total_end_time = time.time()
    total_runtime = total_end_time - total_start_time

    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("=" * 60)

    for algo_name, result in results.items():
        if result['success']:
            print(f"{algo_name}: ✓ 成功 ({result['runtime']:.2f}秒)")
        else:
            print(f"{algo_name}: ✗ 失败 ({result['runtime']:.2f}秒)")

    print(f"\n总运行时间: {total_runtime:.2f}秒 ({total_runtime/60:.2f}分钟)")

    return results

if __name__ == "__main__":
    try:
        results = simple_test()
        print("\n测试完成！")
    except KeyboardInterrupt:
        print("\n\n用户中断了测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
        sys.exit(1)
