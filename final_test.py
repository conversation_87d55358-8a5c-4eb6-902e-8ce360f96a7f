#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试脚本 - 验证算法性能预期
根据项目设计，验证以下预期：
1. EnhancedPipelinedWOA > PipelinedWOA > WOA > 其他算法
2. 流水线化算法在时延、流量、负载均衡方面都应该更优
3. 体现流水线化和增强版的技术优势
"""

import time
import sys
import threading
from standardTopo import NetworkConfiguration
from WOA import WhaleOptimizationAlgorithm
from PopelineWOA import PipelinedWOA
from EnhancedPipelinedWOA import EnhancedPipelinedWOA
import GAsye
from aggre_tree import AggreTree
from SRPT import SRPT

def run_with_timeout(func, timeout_seconds=120):
    """带超时机制运行函数"""
    result = [None]
    exception = [None]
    
    def target():
        try:
            result[0] = func()
        except Exception as e:
            exception[0] = e
    
    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout_seconds)
    
    if thread.is_alive():
        print(f"算法运行超时 ({timeout_seconds}秒)")
        return None
    elif exception[0] is not None:
        print(f"算法运行出错: {str(exception[0])}")
        return None
    else:
        return result[0]

def test_algorithm_safely(algo_name, n, k, network_config):
    """安全测试单个算法"""
    print(f"\n开始测试 {algo_name}...")
    print(f"参数: n={n}, k={k}")
    
    start_time = time.time()
    
    def run_algo():
        if algo_name == 'WOA算法':
            print("  调用 WhaleOptimizationAlgorithm...")
            woa = WhaleOptimizationAlgorithm(nwhales=15, max_iter=25)
            return woa.run(n, k, network_config)
        elif algo_name == 'PipelinedWOA算法':
            print("  调用 PipelinedWOA.run...")
            return PipelinedWOA.run(n, k, network_config)
        elif algo_name == 'EnhancedPipelinedWOA算法':
            print("  调用 EnhancedPipelinedWOA.run...")
            return EnhancedPipelinedWOA.run(n, k, network_config)
        elif algo_name == 'ye_opt算法':
            print("  调用 GAsye.run...")
            ga = GAsye.GeneticAlgorithm_ye()
            return ga.run(n, k, network_config)
        elif algo_name == 'aggre算法':
            print("  调用 AggreTree.run...")
            return AggreTree.run(n, k, network_config)
        elif algo_name == 'srpt算法':
            print("  调用 SRPT.run...")
            return SRPT.run(n, k, network_config)
        else:
            raise ValueError(f"未知算法: {algo_name}")
    
    # 设置超时时间
    timeout_map = {
        'WOA算法': 90,
        'PipelinedWOA算法': 90,
        'EnhancedPipelinedWOA算法': 60,
        'ye_opt算法': 120,
        'aggre算法': 60,
        'srpt算法': 60
    }
    
    timeout = timeout_map.get(algo_name, 90)
    result = run_with_timeout(run_algo, timeout_seconds=timeout)
    
    end_time = time.time()
    runtime = end_time - start_time
    
    if result is not None:
        print(f"  ✓ 算法运行成功")
        print(f"  运行时间: {runtime:.2f}秒")
        print(f"  时延: {result['transmission_delay']:.4f}")
        print(f"  流量: {result['flow_consumption']:.4f}")
        print(f"  负载均衡: {result['std_deviation']:.4f}")
        return {
            'success': True,
            'runtime': runtime,
            'result': result
        }
    else:
        print(f"  ✗ 算法运行失败")
        print(f"  运行时间: {runtime:.2f}秒")
        return {
            'success': False,
            'runtime': runtime,
            'result': None
        }

def final_test():
    """最终测试 - 验证算法性能预期"""
    print("=" * 80)
    print("最终测试脚本 - 验证算法性能预期")
    print("=" * 80)
    
    # 使用合适的参数
    n, k = 20, 40
    network_config = NetworkConfiguration()
    
    print(f"网络配置信息:")
    print(f"  节点数: {len(network_config.nodes)}")
    ny_graph = network_config.get_network_graph()
    print(f"  边数: {len(ny_graph.edges())}")
    print(f"  块大小: {network_config.block_size}")
    
    # 按照预期性能排序的算法列表
    algorithms = [
        # 传统修复树算法（对比基准）
        'ye_opt算法',
        'aggre算法', 
        'srpt算法',
        'WOA算法',
        # 流水线化修复树算法（应该更优）
        'PipelinedWOA算法',
        'EnhancedPipelinedWOA算法'  # 应该是最优的
    ]
    
    results = {}
    total_start_time = time.time()
    
    for algo_name in algorithms:
        try:
            result = test_algorithm_safely(algo_name, n, k, network_config)
            results[algo_name] = result
            
            print("  等待3秒后继续...")
            time.sleep(3)
            
        except KeyboardInterrupt:
            print(f"\n用户中断了 {algo_name} 的测试")
            break
        except Exception as e:
            print(f"\n测试 {algo_name} 时发生未预期错误: {str(e)}")
            results[algo_name] = {
                'success': False,
                'runtime': 0,
                'result': None
            }
    
    total_end_time = time.time()
    total_runtime = total_end_time - total_start_time
    
    print("\n" + "=" * 80)
    print("测试结果总结:")
    print("=" * 80)
    
    # 分类显示结果
    print("\n【传统修复树算法】:")
    traditional_algos = ['ye_opt算法', 'aggre算法', 'srpt算法', 'WOA算法']
    traditional_successful = []
    
    for algo_name in traditional_algos:
        if algo_name in results:
            result = results[algo_name]
            if result['success']:
                print(f"  {algo_name}: ✓ 成功 ({result['runtime']:.2f}秒)")
                print(f"    - 时延: {result['result']['transmission_delay']:.4f}")
                print(f"    - 流量: {result['result']['flow_consumption']:.4f}")
                print(f"    - 负载均衡: {result['result']['std_deviation']:.4f}")
                traditional_successful.append((algo_name, result))
            else:
                print(f"  {algo_name}: ✗ 失败 ({result['runtime']:.2f}秒)")
    
    print("\n【流水线化修复树算法】:")
    pipeline_algos = ['PipelinedWOA算法', 'EnhancedPipelinedWOA算法']
    pipeline_successful = []
    
    for algo_name in pipeline_algos:
        if algo_name in results:
            result = results[algo_name]
            if result['success']:
                print(f"  {algo_name}: ✓ 成功 ({result['runtime']:.2f}秒)")
                print(f"    - 时延: {result['result']['transmission_delay']:.4f}")
                print(f"    - 流量: {result['result']['flow_consumption']:.4f}")
                print(f"    - 负载均衡: {result['result']['std_deviation']:.4f}")
                pipeline_successful.append((algo_name, result))
            else:
                print(f"  {algo_name}: ✗ 失败 ({result['runtime']:.2f}秒)")
    
    print(f"\n总运行时间: {total_runtime:.2f}秒 ({total_runtime/60:.2f}分钟)")
    
    # 性能分析和验证
    print("\n" + "=" * 80)
    print("性能分析和验证:")
    print("=" * 80)
    
    all_successful = traditional_successful + pipeline_successful
    
    if all_successful:
        print("\n运行时间排序（从快到慢）:")
        sorted_by_time = sorted(all_successful, key=lambda x: x[1]['runtime'])
        for i, (name, result) in enumerate(sorted_by_time, 1):
            print(f"  {i}. {name}: {result['runtime']:.2f}秒")
        
        print("\n时延性能排序（从低到高）:")
        sorted_by_delay = sorted(all_successful, key=lambda x: x[1]['result']['transmission_delay'])
        for i, (name, result) in enumerate(sorted_by_delay, 1):
            delay = result['result']['transmission_delay']
            print(f"  {i}. {name}: {delay:.4f}")
        
        print("\n流量消耗排序（从低到高）:")
        sorted_by_flow = sorted(all_successful, key=lambda x: x[1]['result']['flow_consumption'])
        for i, (name, result) in enumerate(sorted_by_flow, 1):
            flow = result['result']['flow_consumption']
            print(f"  {i}. {name}: {flow:.4f}")
        
        print("\n负载均衡排序（从好到差）:")
        sorted_by_balance = sorted(all_successful, key=lambda x: x[1]['result']['std_deviation'])
        for i, (name, result) in enumerate(sorted_by_balance, 1):
            balance = result['result']['std_deviation']
            print(f"  {i}. {name}: {balance:.4f}")
        
        # 验证预期性能
        print("\n" + "=" * 80)
        print("预期性能验证:")
        print("=" * 80)
        
        # 检查是否有WOA系列算法成功运行
        woa_results = {}
        for name, result in all_successful:
            if 'WOA' in name:
                woa_results[name] = result['result']
        
        if len(woa_results) >= 2:
            print("\n✅ WOA系列算法性能对比:")
            
            # 按预期排序检查
            expected_order = ['WOA算法', 'PipelinedWOA算法', 'EnhancedPipelinedWOA算法']
            available_woa = [name for name in expected_order if name in woa_results]
            
            if len(available_woa) >= 2:
                print("时延对比:")
                for i, name in enumerate(available_woa):
                    delay = woa_results[name]['transmission_delay']
                    print(f"  {name}: {delay:.4f}")
                    if i > 0:
                        prev_name = available_woa[i-1]
                        prev_delay = woa_results[prev_name]['transmission_delay']
                        improvement = ((prev_delay - delay) / prev_delay) * 100
                        if improvement > 0:
                            print(f"    ✓ 比{prev_name}改善了 {improvement:.1f}%")
                        else:
                            print(f"    ⚠ 比{prev_name}性能下降了 {abs(improvement):.1f}%")
        
        # 总体验证
        if 'EnhancedPipelinedWOA算法' in [name for name, _ in all_successful]:
            enhanced_result = next(result for name, result in all_successful if name == 'EnhancedPipelinedWOA算法')
            enhanced_delay = enhanced_result['result']['transmission_delay']
            
            better_count = 0
            total_count = 0
            for name, result in all_successful:
                if name != 'EnhancedPipelinedWOA算法':
                    total_count += 1
                    if result['result']['transmission_delay'] > enhanced_delay:
                        better_count += 1
            
            if better_count == total_count:
                print(f"\n✅ EnhancedPipelinedWOA算法在时延方面优于所有其他算法！")
            else:
                print(f"\n⚠ EnhancedPipelinedWOA算法在时延方面优于 {better_count}/{total_count} 个算法")
    
    return results

if __name__ == "__main__":
    try:
        results = final_test()
        print("\n测试完成！")
    except KeyboardInterrupt:
        print("\n\n用户中断了测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
        sys.exit(1)
