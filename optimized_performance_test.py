#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化性能测试 - 验证优化后的SuperEnhanced是否真正超越Enhanced
"""

import matplotlib.pyplot as plt
import numpy as np
import os
import time
from standardTopo import NetworkConfiguration
from WOA import WhaleOptimizationAlgorithm
from PopelineWOA import PipelinedWOA
from EnhancedPipelinedWOA import EnhancedPipelinedWOA
from SuperEnhancedPipelinedWOA import SuperEnhancedPipelinedWOA

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def run_algorithm_with_timing(algo_name, n, k, network_config):
    """运行算法并记录时间"""
    start_time = time.time()
    
    try:
        if algo_name == 'WOA算法':
            woa = WhaleOptimizationAlgorithm(nwhales=10, max_iter=15)
            result = woa.run(n, k, network_config)
        elif algo_name == 'PipelinedWOA算法':
            result = PipelinedWOA.run(n, k, network_config)
        elif algo_name == 'EnhancedPipelinedWOA算法':
            result = EnhancedPipelinedWOA.run(n, k, network_config)
        elif algo_name == 'SuperEnhancedPipelinedWOA算法':
            result = SuperEnhancedPipelinedWOA.run(n, k, network_config)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 添加执行时间到结果中
        result['execution_time'] = execution_time
        
        return result
        
    except Exception as e:
        print(f"Error running {algo_name}: {str(e)}")
        end_time = time.time()
        return {
            'transmission_delay': 2.0,
            'flow_consumption': 5.0,
            'std_deviation': 0.5,
            'execution_time': end_time - start_time
        }

def comprehensive_optimization_test():
    """全面优化测试"""
    print("=" * 80)
    print("优化后SuperEnhanced vs Enhanced 全面对比测试")
    print("=" * 80)
    
    algorithms = ['EnhancedPipelinedWOA算法', 'SuperEnhancedPipelinedWOA算法']
    
    # 测试多个场景
    test_scenarios = [
        {'name': '小规模', 'n': 6, 'k': 3},
        {'name': '中规模', 'n': 8, 'k': 4},
        {'name': '大规模', 'n': 10, 'k': 5},
        {'name': '超大规模', 'n': 12, 'k': 6}
    ]
    
    all_results = {}
    
    for scenario in test_scenarios:
        print(f"\n测试场景: {scenario['name']} - RS码({scenario['n']}, {scenario['k']})")
        
        network_config = NetworkConfiguration()
        scenario_results = {}
        
        for algo_name in algorithms:
            print(f"  运行 {algo_name}...")
            result = run_algorithm_with_timing(algo_name, scenario['n'], scenario['k'], network_config)
            scenario_results[algo_name] = result
            
            print(f"    时延: {result['transmission_delay']:.4f}")
            print(f"    流量: {result['flow_consumption']:.4f}")
            print(f"    负载均衡: {result['std_deviation']:.4f}")
            print(f"    执行时间: {result['execution_time']:.2f}秒")
        
        all_results[scenario['name']] = scenario_results
    
    return all_results

def analyze_optimization_results(all_results):
    """分析优化结果"""
    print("\n" + "=" * 80)
    print("优化结果分析")
    print("=" * 80)
    
    enhanced_name = 'EnhancedPipelinedWOA算法'
    super_name = 'SuperEnhancedPipelinedWOA算法'
    
    metrics = ['transmission_delay', 'flow_consumption', 'std_deviation', 'execution_time']
    metric_names = ['传输时延', '流量消耗', '负载均衡', '执行时间']
    
    improvements = {metric: [] for metric in metrics}
    
    print("1. 逐场景对比分析:")
    for scenario_name, results in all_results.items():
        print(f"\n  {scenario_name}场景:")
        
        enhanced_result = results[enhanced_name]
        super_result = results[super_name]
        
        for metric, metric_name in zip(metrics, metric_names):
            enhanced_val = enhanced_result[metric]
            super_val = super_result[metric]
            
            if metric == 'execution_time':
                # 执行时间：越小越好
                improvement = ((enhanced_val - super_val) / enhanced_val) * 100
            else:
                # 其他指标：越小越好
                improvement = ((enhanced_val - super_val) / enhanced_val) * 100
            
            improvements[metric].append(improvement)
            
            status = "✅" if improvement > 0 else "❌"
            print(f"    {metric_name}: Enhanced={enhanced_val:.4f}, Super={super_val:.4f}, 改进={improvement:+.1f}% {status}")
    
    print(f"\n2. 总体性能评估:")
    
    for metric, metric_name in zip(metrics, metric_names):
        avg_improvement = np.mean(improvements[metric])
        status = "✅ 显著优化" if avg_improvement > 5 else "⚠️ 轻微优化" if avg_improvement > 0 else "❌ 性能下降"
        print(f"  {metric_name}: 平均改进 {avg_improvement:+.1f}% {status}")
    
    # 计算综合得分
    delay_score = np.mean(improvements['transmission_delay'])
    flow_score = np.mean(improvements['flow_consumption'])
    balance_score = np.mean(improvements['std_deviation'])
    time_score = np.mean(improvements['execution_time'])
    
    comprehensive_score = (delay_score + flow_score + balance_score + time_score) / 4
    
    print(f"\n3. 综合评估:")
    print(f"  综合改进得分: {comprehensive_score:+.1f}%")
    
    if comprehensive_score > 10:
        print("  🎉 SuperEnhanced算法优化成功！显著超越Enhanced算法！")
    elif comprehensive_score > 0:
        print("  ✅ SuperEnhanced算法优化有效，超越Enhanced算法")
    else:
        print("  ❌ SuperEnhanced算法仍需进一步优化")
    
    return improvements

def plot_optimization_comparison(all_results, improvements):
    """绘制优化对比图"""
    print("\n生成优化对比图...")
    
    # 创建2x2子图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('SuperEnhanced算法优化效果验证', fontsize=16, fontweight='bold')
    
    scenarios = list(all_results.keys())
    enhanced_name = 'EnhancedPipelinedWOA算法'
    super_name = 'SuperEnhancedPipelinedWOA算法'
    
    # 1. 传输时延对比
    ax1 = axes[0, 0]
    enhanced_delays = [all_results[s][enhanced_name]['transmission_delay'] for s in scenarios]
    super_delays = [all_results[s][super_name]['transmission_delay'] for s in scenarios]
    
    x = np.arange(len(scenarios))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, enhanced_delays, width, label='Enhanced', color='#e377c2', alpha=0.8)
    bars2 = ax1.bar(x + width/2, super_delays, width, label='SuperEnhanced', color='#ff1493', alpha=0.8)
    
    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                   f'{height:.3f}', ha='center', va='bottom', fontsize=10)
    
    ax1.set_title('传输时延对比', fontsize=14)
    ax1.set_ylabel('传输时延')
    ax1.set_xticks(x)
    ax1.set_xticklabels(scenarios)
    ax1.legend()
    ax1.grid(True, alpha=0.3, axis='y')
    
    # 2. 流量消耗对比
    ax2 = axes[0, 1]
    enhanced_flows = [all_results[s][enhanced_name]['flow_consumption'] for s in scenarios]
    super_flows = [all_results[s][super_name]['flow_consumption'] for s in scenarios]
    
    bars1 = ax2.bar(x - width/2, enhanced_flows, width, label='Enhanced', color='#e377c2', alpha=0.8)
    bars2 = ax2.bar(x + width/2, super_flows, width, label='SuperEnhanced', color='#ff1493', alpha=0.8)
    
    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                   f'{height:.3f}', ha='center', va='bottom', fontsize=10)
    
    ax2.set_title('流量消耗对比', fontsize=14)
    ax2.set_ylabel('流量消耗')
    ax2.set_xticks(x)
    ax2.set_xticklabels(scenarios)
    ax2.legend()
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 3. 执行时间对比
    ax3 = axes[1, 0]
    enhanced_times = [all_results[s][enhanced_name]['execution_time'] for s in scenarios]
    super_times = [all_results[s][super_name]['execution_time'] for s in scenarios]
    
    bars1 = ax3.bar(x - width/2, enhanced_times, width, label='Enhanced', color='#e377c2', alpha=0.8)
    bars2 = ax3.bar(x + width/2, super_times, width, label='SuperEnhanced', color='#ff1493', alpha=0.8)
    
    # 添加数值标签
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height,
                   f'{height:.2f}s', ha='center', va='bottom', fontsize=10)
    
    ax3.set_title('执行时间对比', fontsize=14)
    ax3.set_ylabel('执行时间 (秒)')
    ax3.set_xticks(x)
    ax3.set_xticklabels(scenarios)
    ax3.legend()
    ax3.grid(True, alpha=0.3, axis='y')
    
    # 4. 改进百分比
    ax4 = axes[1, 1]
    metrics = ['transmission_delay', 'flow_consumption', 'std_deviation', 'execution_time']
    metric_names = ['时延', '流量', '负载均衡', '执行时间']
    avg_improvements = [np.mean(improvements[metric]) for metric in metrics]
    
    colors = ['#ff1493' if imp > 0 else '#ff6b6b' for imp in avg_improvements]
    bars = ax4.bar(metric_names, avg_improvements, color=colors, alpha=0.8)
    
    # 添加数值标签
    for bar, imp in zip(bars, avg_improvements):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height,
               f'{imp:+.1f}%', ha='center', va='bottom' if imp > 0 else 'top', 
               fontsize=12, fontweight='bold')
    
    ax4.set_title('平均改进百分比', fontsize=14)
    ax4.set_ylabel('改进百分比 (%)')
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax4.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('results/optimized_performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 优化对比图已保存: results/optimized_performance_comparison.png")

def main():
    """主函数"""
    if not os.path.exists('results'):
        os.makedirs('results')
    
    try:
        # 运行全面优化测试
        all_results = comprehensive_optimization_test()
        
        # 分析优化结果
        improvements = analyze_optimization_results(all_results)
        
        # 绘制对比图
        plot_optimization_comparison(all_results, improvements)
        
        print(f"\n🎉 优化性能测试完成！")
        
    except Exception as e:
        print(f"测试出错: {str(e)}")
        raise

if __name__ == "__main__":
    main()
